import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Utility class for exporting files to the native file system
class FileExportUtil {
  /// Export a markdown file with the given content
  /// Returns true if file was successfully saved, false if user cancelled or error occurred
  static Future<bool> exportMarkdownFile(String content, {String prefix = 'ideabooks'}) async {
    try {
      Logger.debug('Starting markdown file export');
      
      // Get the current timestamp for filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = '${prefix}_export_$timestamp.md';
      
      // Write file to temporary directory first
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$filename');
      await tempFile.writeAsString(content);
      
      // Use platform channel to save file natively
      const platform = MethodChannel('com.noeji.app/file_export');
      final result = await platform.invokeMethod('saveMarkdownFile', {
        'filename': filename,
        'content': content,
      });
      
      final success = result as bool;
      if (success) {
        Logger.debug('Markdown file export completed successfully');
      } else {
        Logger.debug('Markdown file export cancelled by user');
      }
      return success;
    } catch (e) {
      Logger.error('Failed to export markdown file: $e');
      return false;
    }
  }
  
  /// Export an HTML file with the given content
  /// Returns true if file was successfully saved, false if user cancelled or error occurred
  static Future<bool> exportHtmlFile(String content, {String prefix = 'ideabooks'}) async {
    try {
      Logger.debug('Starting HTML file export');
      
      // Get the current timestamp for filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename = '${prefix}_export_$timestamp.html';
      
      // Use platform channel to save file natively
      const platform = MethodChannel('com.noeji.app/file_export');
      final result = await platform.invokeMethod('saveHtmlFile', {
        'filename': filename,
        'content': content,
      });
      
      final success = result as bool;
      if (success) {
        Logger.debug('HTML file export completed successfully');
      } else {
        Logger.debug('HTML file export cancelled by user');
      }
      return success;
    } catch (e) {
      Logger.error('Failed to export HTML file: $e');
      return false;
    }
  }

}