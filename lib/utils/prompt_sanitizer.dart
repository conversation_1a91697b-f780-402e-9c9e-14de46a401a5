/// Utility class for sanitizing content used in LLM prompts
class PromptSanitizer {
  /// Sanitize content for single-line use (user input, ideabook names, etc.)
  /// Replaces newlines with spaces and sanitizes quotes and braces
  static String sanitizeSingleLine(String content) {
    return content
        .replaceAll('\n', ' ')
        .replaceAll('\r', ' ')
        .replaceAll('"""', '""')
        .replaceAll('{{', '{')
        .replaceAll('}}', '}');
  }

  /// Sanitize content while preserving some structure (for ideas, chat messages)
  /// Same as single-line but makes the intent clearer
  static String sanitizeContent(String content) {
    return content
        .replaceAll('\n', ' ')
        .replaceAll('\r', ' ')
        .replaceAll('"""', '""')
        .replaceAll('{{', '{')
        .replaceAll('}}', '}');
  }

  /// Sanitize content for multi-line use (ideas_text, preserving line structure)
  /// Only sanitizes quotes and braces, preserves newlines
  static String sanitizeMultiLine(String content) {
    return content
        .replaceAll('"""', '""')
        .replaceAll('{{', '{')
        .replaceAll('}}', '}');
  }
}
