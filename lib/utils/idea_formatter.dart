import 'package:noeji/models/idea.dart';
import 'package:noeji/models/ideabook.dart';
import 'package:noeji/utils/prompt_sanitizer.dart';

/// Utility class for formatting ideas text for LLM prompts
class IdeaFormatter {
  /// Formats ideas text for LLM prompts with date and optional todo state
  ///
  /// Format: `* date | [state |] idea content`
  /// - If ideabook is in todo mode, includes todo state (✓ or ○)
  /// - Otherwise, uses standard format without state
  static String formatIdeasForPrompt(List<Idea> ideas, {Ideabook? ideabook}) {
    if (ideas.isEmpty) {
      return "No ideas found in this ideabook yet.";
    }

    final bool isTodoMode = ideabook?.showAsTodoList ?? false;
    final legend =
        isTodoMode
            ? "(format: creation_date | is_todo_completed? (Y or N) | content)"
            : "(format: creation_date | content)";

    final ideasText = ideas
        .map((idea) {
          // Extract only the date part (YYYY-MM-DD) in local timezone
          final dateOnly = idea.createdAt.toLocal().toString().split(' ')[0];
          final ideaContent = PromptSanitizer.sanitizeContent(idea.content);

          if (isTodoMode) {
            // Include todo state for todo mode ideabooks
            final todoState = (idea.isDone ?? false) ? 'Y' : 'N';
            return '* $dateOnly | $todoState | $ideaContent';
          } else {
            // Standard format without todo state
            return '* $dateOnly | $ideaContent';
          }
        })
        .join('\n');
    return '$legend\n$ideasText';
  }
}
