import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/idea_edit_provider.dart';
import 'package:noeji/ui/providers/idea_edit_state_provider.dart';
import 'package:noeji/ui/providers/placeholder_idea_provider.dart';
import 'package:noeji/ui/providers/firestore_idea_provider.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/services/firebase/firestore_listener_pool.dart';

import 'package:noeji/utils/logger.dart';

/// Helper class for managing edit mode transitions with auto-save
class EditModeHelper {
  /// Auto-save and exit any existing idea edit mode
  /// Returns true if successfully handled, false if there was an error
  /// [excludeIdeaId] - if provided, will not auto-save this specific idea (useful when switching between ideas)
  static Future<bool> exitIdeaEditModeWithAutoSave(
    WidgetRef ref, {
    String? excludeIdeaId,
  }) async {
    final currentEditIdeaId = ref.read(ideaEditProvider);

    if (currentEditIdeaId == null) {
      // No idea is currently in edit mode
      return true;
    }

    // If the current edit idea is the one we want to exclude, just exit without saving
    if (excludeIdeaId != null && currentEditIdeaId == excludeIdeaId) {
      Logger.debug('Skipping auto-save for excluded idea: $currentEditIdeaId');
      ref.read(ideaEditStateProvider.notifier).state = const IdeaEditState();
      ref.read(ideaEditProvider.notifier).state = null;
      return true;
    }

    try {
      Logger.debug(
        'Auto-saving and exiting edit mode for idea: $currentEditIdeaId',
      );

      // Get the current edit state to check if there's content to save
      final editState = ref.read(ideaEditStateProvider);

      // Get references we need before clearing state
      final listenerPool = ref.read(firestoreListenerPoolProvider);
      final firestoreService = ref.read(firestoreServiceProvider);

      // Clear the edit state and exit edit mode FIRST to avoid widget disposal issues
      ref.read(ideaEditStateProvider.notifier).state = const IdeaEditState();
      ref.read(ideaEditProvider.notifier).state = null;

      // Now perform auto-save if needed (using captured references)
      if (editState.currentEditContent != null &&
          editState.currentIdeabookId != null) {
        // Get the original idea to compare against
        final ideaStream = listenerPool.getIdeaStream(
          editState.currentIdeabookId!,
          currentEditIdeaId,
        );
        final originalIdea = await ideaStream.first;

        if (originalIdea != null) {
          final originalContent = originalIdea.content.trim();
          final editedContent = editState.currentEditContent!.trim();

          // Check if there are actual changes
          if (editedContent != originalContent && editedContent.isNotEmpty) {
            Logger.debug(
              'Auto-saving changes for idea $currentEditIdeaId: "$editedContent"',
            );

            // Perform the auto-save using captured service reference
            try {
              final updatedIdea = originalIdea.copyWith(
                content: editedContent,
                updatedAt: DateTime.now(),
              );

              final success = await firestoreService.updateIdea(
                editState.currentIdeabookId!,
                updatedIdea,
              );

              if (success) {
                Logger.debug('Auto-saved idea $currentEditIdeaId successfully');
              } else {
                Logger.error('Failed to auto-save idea $currentEditIdeaId');
              }
            } catch (saveError) {
              Logger.error(
                'Error during auto-save for idea $currentEditIdeaId',
                saveError,
              );
            }
          } else {
            Logger.debug('No changes to auto-save for idea $currentEditIdeaId');
          }
        }
      }

      Logger.debug(
        'Successfully exited edit mode for idea: $currentEditIdeaId',
      );
      return true;
    } catch (e) {
      Logger.error('Error exiting idea edit mode with auto-save', e);
      // Try to clear state if ref is still valid
      try {
        ref.read(ideaEditStateProvider.notifier).state = const IdeaEditState();
        ref.read(ideaEditProvider.notifier).state = null;
      } catch (refError) {
        Logger.debug(
          'Could not clear edit state due to widget disposal (this is expected)',
        );
      }
      return false;
    }
  }

  /// Auto-save and exit any existing placeholder idea mode
  /// Returns true if successfully handled, false if there was an error
  static Future<bool> exitPlaceholderModeWithAutoSave(WidgetRef ref) async {
    final currentPlaceholder = ref.read(placeholderIdeaProvider);

    if (currentPlaceholder == null) {
      // No placeholder is currently active
      return true;
    }

    try {
      Logger.debug(
        'Auto-saving and exiting placeholder mode for ideabook: ${currentPlaceholder.ideabookId}',
      );

      // Check if the placeholder has any content to save
      if (currentPlaceholder.content.trim().isNotEmpty) {
        // Auto-save the placeholder content
        final success = await _savePlaceholderContent(ref, currentPlaceholder);
        if (!success) {
          Logger.error('Failed to auto-save placeholder content');
          return false;
        }
      } else {
        // No content to save, just cancel the placeholder
        PlaceholderIdeaHelper.cancelPlaceholder(ref);
      }

      Logger.debug(
        'Successfully exited placeholder mode for ideabook: ${currentPlaceholder.ideabookId}',
      );
      return true;
    } catch (e) {
      Logger.error('Error exiting placeholder mode with auto-save', e);
      return false;
    }
  }

  /// Helper method to save placeholder content
  static Future<bool> _savePlaceholderContent(
    WidgetRef ref,
    PlaceholderIdea placeholder,
  ) async {
    try {
      // Import the notifier for creating ideas
      final notifier = ref.read(
        firestoreIdeasNotifierProvider(placeholder.ideabookId).notifier,
      );

      // Create the idea (this will be fire-and-forget like in the UI)
      notifier.createIdea(content: placeholder.content).catchError((e) {
        Logger.error(
          'Background save failed for auto-saved placeholder idea',
          e,
        );
        // Return a fake idea as fallback - this won't be used since it's fire-and-forget
        return Idea(
          id: 'error',
          content: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      });

      // Remove the placeholder from the UI immediately
      PlaceholderIdeaHelper.cancelPlaceholder(ref);

      Logger.debug(
        'Auto-saved placeholder idea content: "${placeholder.content.substring(0, placeholder.content.length.clamp(0, 50))}${placeholder.content.length > 50 ? "..." : ""}"',
      );
      return true;
    } catch (e) {
      Logger.error('Error saving placeholder content', e);
      return false;
    }
  }
}
