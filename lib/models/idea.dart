import 'package:noeji/models/base_model.dart';

/// Sentinel object for distinguishing between null and unspecified values
const Object _sentinel = Object();

/// Represents an Idea, which is a user input captured by voice or text
class Idea extends BaseModel {
  /// Content of the idea (field 'c' in Firestore)
  final String content;

  /// Sort order value (field 's' in Firestore)
  /// This is only present if the idea has been manually reordered
  final double? sortOrder;

  /// Local audio file name (field 'f' in Firestore)
  /// This is present when the idea is created from audio but transcription hasn't completed yet
  final String? audioFileName;

  /// Transcription error flag (field 'e' in Firestore)
  /// This is set to true when transcription fails after retries
  final bool? transcriptionError;

  /// Whether this idea is completed in todo mode (field 'd' in Firestore)
  /// This is set to true when the idea is marked as done in todo list mode
  final bool? isDone;

  /// Creates a new Idea instance
  const Idea({
    required super.id,
    required this.content,
    this.sortOrder,
    this.audioFileName,
    this.transcriptionError,
    this.isDone,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Get the effective sort value for this idea
  /// If sortOrder is not null, use it, otherwise use createdAt timestamp
  double getEffectiveSortValue() {
    return sortOrder ?? createdAt.millisecondsSinceEpoch.toDouble();
  }

  /// Check if this idea is in a transcribing state
  bool get isTranscribing {
    return (content.isEmpty || content.trim().isEmpty) &&
        (audioFileName?.isNotEmpty ?? false) &&
        (transcriptionError != true);
  }

  /// Check if this idea has a transcription error
  bool get hasTranscriptionError {
    return transcriptionError == true;
  }

  /// Check if this idea is empty (no content and no audio file)
  bool get isEmpty {
    return (content.isEmpty || content.trim().isEmpty) &&
        (audioFileName?.isEmpty ?? true);
  }

  /// Check if this idea is a regular completed idea
  bool get isCompleted {
    return content.isNotEmpty &&
        content.trim().isNotEmpty &&
        (audioFileName?.isEmpty ?? true);
  }

  @override
  Idea copyWith({
    String? id,
    String? content,
    Object? sortOrder = _sentinel,
    Object? audioFileName = _sentinel,
    Object? transcriptionError = _sentinel,
    Object? isDone = _sentinel,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Idea(
      id: id ?? this.id,
      content: content ?? this.content,
      sortOrder: sortOrder == _sentinel ? this.sortOrder : sortOrder as double?,
      audioFileName:
          audioFileName == _sentinel
              ? this.audioFileName
              : audioFileName as String?,
      transcriptionError:
          transcriptionError == _sentinel
              ? this.transcriptionError
              : transcriptionError as bool?,
      isDone: isDone == _sentinel ? this.isDone : isDone as bool?,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Idea{id: $id, sortOrder: $sortOrder, audioFileName: $audioFileName, transcriptionError: $transcriptionError, isDone: $isDone}';
  }
}
