import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/screens/ideabooks_list_screen.dart';
import 'package:noeji/utils/logger.dart';

/// Widget that routes users to the appropriate screen based on their user state
/// Implements the 4-state monetization model:
/// - newUser: Shows main app with free tier limits (paywall shown when limits hit)
/// - trialUser/proUser: Shows main app
/// - freeUser: Shows main app with read-only access
/// - unknown: Shows loading
class UserStateRouter extends ConsumerWidget {
  const UserStateRouter({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the real-time user state
    final userStateAsync = ref.watch(realtimeUserStateProvider);

    return userStateAsync.when(
      loading: () {
        Logger.debug(
          'UserStateRouter: User state loading, showing loading indicator',
        );
        return const Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading...'),
              ],
            ),
          ),
        );
      },
      error: (error, stackTrace) {
        Logger.error('UserStateRouter: Error loading user state', error);
        // On error, default to showing the main app
        // The app will handle the error state internally
        return const IdeabooksListScreen();
      },
      data: (userState) {
        Logger.debug('UserStateRouter: User state is $userState');

        switch (userState) {
          case UserState.newUser:
            // New users must see paywall before accessing the app
            Logger.debug('UserStateRouter: Showing main app for new user');
            return const IdeabooksListScreen();

          case UserState.trialUser:
          case UserState.proUser:
          case UserState.freeUser:
            // These users can access the main app
            // The app will handle the differences in functionality internally
            Logger.debug(
              'UserStateRouter: Showing main app for user state: $userState',
            );
            return const IdeabooksListScreen();

          case UserState.unknown:
            // Unknown state, show loading
            Logger.debug(
              'UserStateRouter: Unknown user state, showing loading',
            );
            return const Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Determining user status...'),
                  ],
                ),
              ),
            );
        }
      },
    );
  }
}
