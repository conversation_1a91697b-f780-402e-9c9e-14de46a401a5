import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/ideabook_color_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/color_utils.dart';
import 'package:noeji/utils/logger.dart';

/// Widget for selecting a color for an ideabook
class ColorPicker extends ConsumerWidget {
  /// The ideabook ID to update
  final String? ideabookId;

  /// The current color of the ideabook
  final IdeabookColor currentColor;

  /// Callback when a color is selected
  final Function(IdeabookColor)? onColorSelected;

  /// Constructor
  const ColorPicker({
    super.key,
    this.ideabookId,
    required this.currentColor,
    this.onColorSelected,
  });

  /// Get the ordered list of colors in consistent order
  /// Order: purple → blue → green → yellow → orange → red
  List<IdeabookColor> _getOrderedColors() {
    return [
      IdeabookColor.purple,
      IdeabookColor.blue,
      IdeabookColor.green,
      IdeabookColor.yellow,
      IdeabookColor.orange,
      IdeabookColor.red,
    ];
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final orderedColors = _getOrderedColors();

    // Calculate width for each color (6 total colors)
    final colorWidth = screenWidth / 6;

    return GestureDetector(
      // Handle taps outside color squares to close the menu
      onTap: () {
        if (onColorSelected != null) {
          // Call the callback to close the menu
          onColorSelected!(currentColor);
        }
      },
      child: Container(
        width: screenWidth,
        height: double.infinity,
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Row(
          children: [
            // Show all colors in consistent order
            ...orderedColors.map((color) {
              final isCurrentColor = color == currentColor;
              return GestureDetector(
                onTap: () {
                  // Don't provide haptic feedback for current color
                  if (!isCurrentColor) {
                    // Provide haptic feedback for color selection
                    HapticFeedbackService.trigger(HapticAction.colorPicker);
                    
                    if (ideabookId != null) {
                      // Update the ideabook color using the provider
                      final updateColor = ref.read(updateIdeabookColorProvider);
                      Logger.debug(
                        'Color selected: ${color.name} for ideabook $ideabookId',
                      );
                      updateColor(ideabookId!, color);
                    }
                  }

                  // Close the menu after selection
                  if (onColorSelected != null) {
                    onColorSelected!(color);
                  }
                },
                // Prevent tap events from propagating to parent widgets
                behavior: HitTestBehavior.opaque,
                child: Container(
                  width: colorWidth,
                  height: double.infinity,
                  color: NoejiTheme.getIdeabookColor(context, color.index),
                  child: isCurrentColor
                      ? Icon(
                          Icons.check_circle,
                          color: ColorUtils.getContrastingTextColor(
                            NoejiTheme.getIdeabookColor(context, color.index),
                          ),
                          size: 20,
                        )
                      : null,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
