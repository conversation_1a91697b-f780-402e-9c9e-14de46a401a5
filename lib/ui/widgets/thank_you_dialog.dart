import 'package:flutter/material.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving thank you dialog preferences
class ThankYouDialogStorage {
  static const String _thankYouDialogShownKey = 'noeji_thank_you_dialog_shown';

  /// Check if the thank you dialog should be shown
  /// Returns true if the dialog should be shown (has not been shown before)
  /// Returns false if the dialog should not be shown (has been shown before)
  static Future<bool> shouldShowThankYouDialog() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasBeenShown = prefs.getBool(_thankYouDialogShownKey) ?? false;

      Logger.debug(
        'Checking thank you dialog status: hasBeenShown = $hasBeenShown',
      );

      // If the dialog has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show thank you dialog: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error('Error checking if thank you dialog should be shown', e);
      return false;
    }
  }

  /// Save that the thank you dialog has been shown
  static Future<bool> saveThankYouDialogShown() async {
    try {
      Logger.debug('Attempting to save thank you dialog shown status');
      final prefs = await SharedPreferences.getInstance();

      final result = await prefs.setBool(_thankYouDialogShownKey, true);

      if (result) {
        Logger.debug('Thank you dialog shown status saved successfully');
      } else {
        Logger.error('Failed to save thank you dialog shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving thank you dialog shown status', e);
      return false;
    }
  }

  /// Reset the thank you dialog shown status
  /// This is useful for testing
  static Future<bool> resetThankYouDialogShown() async {
    try {
      Logger.debug('Attempting to reset thank you dialog shown status');
      final prefs = await SharedPreferences.getInstance();

      final result = await prefs.remove(_thankYouDialogShownKey);

      if (result) {
        Logger.debug('Thank you dialog shown status reset successfully');
      } else {
        Logger.error('Failed to reset thank you dialog shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting thank you dialog shown status', e);
      return false;
    }
  }
}

/// Utility class for showing thank you dialog after successful payment
class ThankYouDialog {
  /// Show the thank you dialog after successful payment
  /// This dialog is shown only once per app installation
  static Future<void> showThankYouDialog(BuildContext context) async {
    try {
      // Check if dialog should be shown
      final shouldShow = await ThankYouDialogStorage.shouldShowThankYouDialog();

      if (!shouldShow) {
        Logger.debug('Thank you dialog already shown, skipping');
        return;
      }

      // Check if context is still valid
      if (!context.mounted) {
        Logger.debug('Context no longer mounted, skipping thank you dialog');
        return;
      }

      // Show the dialog
      await showDialog<void>(
        context: context,
        barrierDismissible: true, // Allow dismissing by tapping outside
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: Text(
              'Thank You! 🎉',
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              'Your support means the world to us! 🌟✨ We\'re thrilled to have you as part of the Noeji Pro community. Enjoy all the amazing features! 🚀',
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(
                  'OK',
                  style: NoejiTheme.textStylesOf(dialogContext).buttonText,
                ),
              ),
            ],
          );
        },
      );

      // Mark the dialog as shown
      await ThankYouDialogStorage.saveThankYouDialogShown();
      Logger.debug('Thank you dialog shown and marked as seen');
    } catch (e) {
      Logger.error('Error showing thank you dialog', e);
    }
  }
}
