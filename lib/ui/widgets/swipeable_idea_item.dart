import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/llm/llm_providers.dart';
import 'package:noeji/services/llm/llm_service.dart';
import 'package:noeji/services/transcription/background_transcription_service.dart';
import 'package:noeji/services/firebase/firestore_listener_pool.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/ui/providers/idea_edit_provider.dart';
import 'package:noeji/ui/providers/idea_edit_state_provider.dart';
import 'package:noeji/ui/providers/idea_edit_recording_provider.dart';
import 'package:noeji/ui/providers/idea_edit_transcription_provider.dart';
import 'package:noeji/ui/providers/ideabook_provider.dart';
import 'package:noeji/ui/widgets/ideabook_detail_bottom_panel_controller.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/advanced_audio_recording_panel.dart';
import 'package:noeji/ui/widgets/common/swipeable_item.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/edit_mode_helper.dart';

/// Provider to track which idea is currently in swipe mode
final swipedIdeaIdProvider = StateProvider<String?>((ref) => null);

/// Provider to track optimistic completion states for ideas
/// This allows immediate UI updates while Firestore updates happen in background
final optimisticCompletionProvider = StateProvider<Map<String, bool>>((ref) => {});

/// Provider to track optimistic edit content for ideas
/// This allows immediate UI updates during inline editing before Firestore sync
final optimisticEditContentProvider = StateProvider<Map<String, String>>((ref) => {});

/// Widget for displaying an individual idea in the list with swipe functionality
class SwipeableIdeaItem extends ConsumerStatefulWidget {
  /// The idea to display
  final Idea idea;

  /// The ideabook ID this idea belongs to
  final String ideabookId;

  // Context menu constants
  static const double _maxSwipeExtent =
      60.0; // Maximum swipe distance for context menu (1 icon)

  /// Constructor
  const SwipeableIdeaItem({
    super.key,
    required this.idea,
    required this.ideabookId,
  });

  @override
  ConsumerState<SwipeableIdeaItem> createState() => _SwipeableIdeaItemState();
}

class _SwipeableIdeaItemState extends ConsumerState<SwipeableIdeaItem> {
  // Text controller for editing
  late TextEditingController _textEditingController;
  late FocusNode _focusNode;

  // Flag to track if we've appended text
  bool _hasAppendedText = false;
  // Flag to track if we've just appended text and should not reset
  bool _justAppendedText = false;
  // Flag to control whether the text field should be focused
  bool _shouldFocusTextField = true;
  // Store the current text to prevent resets
  String _currentText = "";

  // Cached max idea words limit
  int? _maxIdeaWords;

  @override
  void initState() {
    super.initState();

    // Initialize with the idea content
    _currentText = widget.idea.content;
    Logger.debug(
      'SwipeableIdeaItem initState - Idea ID: ${widget.idea.id}, Content: "${_currentText.substring(0, _currentText.length.clamp(0, 20))}${_currentText.length > 20 ? "..." : ""}"',
    );
    _textEditingController = TextEditingController(text: _currentText);
    _focusNode = FocusNode();

    // Initialize the max idea words limit
    _initializeMaxIdeaWords();

    // Add listener to text controller to debug changes and update edit state
    _textEditingController.addListener(() {
      final text = _textEditingController.text;
      Logger.debug('TextEditingController updated: ${text.length} chars');

      // Store the current text to prevent resets
      if (text != _currentText) {
        _currentText = text;
        Logger.debug('Updated _currentText to: ${_currentText.length} chars');

        // Update the edit state provider if this idea is currently being edited
        final currentEditId = ref.read(ideaEditProvider);
        if (currentEditId == widget.idea.id) {
          ref.read(ideaEditStateProvider.notifier).state = IdeaEditState(
            currentEditIdeaId: widget.idea.id,
            currentEditContent: text,
            currentIdeabookId: widget.ideabookId,
          );
        }
      }
    });

    // Add a listener to the transcription provider
    Future.microtask(() {
      ref.listenManual(ideaEditTranscriptionProvider, (previous, next) {
        if (next != null && next.ideaId == widget.idea.id) {
          Logger.debug(
            'Transcription provider updated in listener: ${next.transcribedText}',
          );
          _appendTranscribedText(next.transcribedText);
          // Reset the provider
          ref.read(ideaEditTranscriptionProvider.notifier).state = null;
        }
      });
    });
  }

  /// Initialize the maximum idea words limit from the provider
  Future<void> _initializeMaxIdeaWords() async {
    try {
      _maxIdeaWords = await ref.read(ideaMaxWordsProvider.future);
      Logger.debug(
        'SwipeableIdeaItem: Max idea words initialized: $_maxIdeaWords',
      );
    } catch (e) {
      Logger.error(
        'SwipeableIdeaItem: Failed to get max idea words, using fallback',
        e,
      );
      _maxIdeaWords = 1000; // Fallback to free tier limit
    }
  }

  @override
  void didUpdateWidget(SwipeableIdeaItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the idea content has changed (e.g., from Firestore update)
    if (widget.idea.content != oldWidget.idea.content) {
      Logger.debug('SwipeableIdeaItem didUpdateWidget - Idea content changed');
      Logger.debug(
        'Old content: "${oldWidget.idea.content.substring(0, oldWidget.idea.content.length.clamp(0, 20))}${oldWidget.idea.content.length > 20 ? "..." : ""}"',
      );
      Logger.debug(
        'New content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
      );

      // CONFLICT RESOLUTION: Check if we have optimistic content for this idea
      final optimisticEdits = ref.read(optimisticEditContentProvider);
      final hasOptimisticEdit = optimisticEdits.containsKey(widget.idea.id);

      if (hasOptimisticEdit) {
        Logger.debug(
          'Not updating from Firestore because we have optimistic edit in progress for idea ${widget.idea.id}',
        );
        // If the Firestore update matches our optimistic content, we can clear the optimistic state
        final optimisticContent = optimisticEdits[widget.idea.id]!;
        if (widget.idea.content.trim() == optimisticContent.trim()) {
          Logger.debug(
            'Firestore content matches optimistic content, clearing optimistic state for idea ${widget.idea.id}',
          );
          // Delay the provider modification to avoid modifying during widget tree build
          Future(() {
            if (mounted) {
              final currentOptimisticEdits = ref.read(optimisticEditContentProvider);
              final updatedOptimisticEdits = Map<String, String>.from(currentOptimisticEdits);
              updatedOptimisticEdits.remove(widget.idea.id);
              ref.read(optimisticEditContentProvider.notifier).state = updatedOptimisticEdits;
            }
          });
        }
        return; // Don't update the text controller when we have optimistic content
      }

      // Don't update if we just appended text
      if (_justAppendedText) {
        Logger.debug(
          'Not updating text controller because we just appended text',
        );
        return;
      }

      // Only update if we're not in edit mode or if the user hasn't made changes
      final isEditing = ref.read(ideaEditProvider) == widget.idea.id;
      final userHasEditedText =
          _textEditingController.text != oldWidget.idea.content;

      if (!isEditing || !userHasEditedText) {
        Logger.debug('Updating _currentText with new idea content');
        _currentText = widget.idea.content;

        // Only update the controller if it's different to avoid cursor position reset
        if (_textEditingController.text != _currentText) {
          _textEditingController.text = _currentText;
        }
      } else {
        Logger.debug('Not updating text controller because user is editing');
      }
    }

    // Check if edit mode has been entered or exited
    final wasEditing = ref.read(ideaEditProvider) == oldWidget.idea.id;
    final isEditing = ref.read(ideaEditProvider) == widget.idea.id;

    if (wasEditing && !isEditing) {
      // We've exited edit mode - check if we need to auto-save
      final hasUnsavedChanges =
          _textEditingController.text.trim() != widget.idea.content.trim();

      if (hasUnsavedChanges) {
        Logger.debug(
          'Auto-saving changes before exiting edit mode for idea ${widget.idea.id}',
        );
        // Schedule auto-save after the build is complete
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _autoSaveChanges();
          }
        });
      } else {
        Logger.debug('Exited edit mode, no changes to save');
        _resetToOriginalContent();
      }
    } else if (!wasEditing && isEditing) {
      // We've entered edit mode, reset to original content
      Logger.debug('Entered edit mode, resetting to original content');
      _resetToOriginalContent();
    }

    // Tracking flag no longer needed
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// Insert transcribed text at the current cursor position
  void _appendTranscribedText(String transcribedText) {
    Logger.debug(
      'Inserting transcribed text at cursor position for idea ${widget.idea.id}',
    );
    Logger.debug('Current text length: ${_textEditingController.text.length}');
    Logger.debug('Transcribed text length: ${transcribedText.length}');

    // Get the current text and cursor position
    final currentText = _textEditingController.text;
    final selection = _textEditingController.selection;

    // Determine where to insert the transcribed text
    String newText;
    int newCursorPosition;

    if (selection.isValid) {
      // Insert at cursor position
      final beforeCursor = currentText.substring(0, selection.start);
      final afterCursor = currentText.substring(selection.end);

      // Create the new text with the transcription inserted at cursor (no space added)
      newText = beforeCursor + transcribedText + afterCursor;

      // Calculate new cursor position (after the inserted text)
      newCursorPosition = beforeCursor.length + transcribedText.length;
    } else {
      // If no valid selection, append to the end (no space added)
      newText =
          currentText.isEmpty ? transcribedText : currentText + transcribedText;
      newCursorPosition = newText.length;
    }

    Logger.debug('New text length: ${newText.length}');

    // Update our stored text first
    _currentText = newText;
    _hasAppendedText = true;
    _justAppendedText = true; // Set flag to prevent resetting in _buildEditRow

    // Update the text controller
    _textEditingController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.fromPosition(
        TextPosition(offset: newCursorPosition),
      ),
    );

    // Force a rebuild
    setState(() {
      // This ensures the UI updates with the new text
      Logger.debug('Forcing UI update after inserting text at cursor position');
    });

    // Re-focus the text field to allow immediate editing
    _focusNode.requestFocus();

    Logger.debug(
      'Inserted transcribed text at cursor position for idea ${widget.idea.id}',
    );
  }

  @override
  Widget build(BuildContext context) {
    // Use the generic SwipeableItem widget
    return SwipeableItem(
      itemId: widget.idea.id,
      swipedItemProvider: swipedIdeaIdProvider,
      editItemProvider: ideaEditProvider,
      maxSwipeExtent: SwipeableIdeaItem._maxSwipeExtent,
      contextMenuBuilder:
          (context, totalMenuWidth) =>
              _buildContextMenu(context, totalMenuWidth),
      contentBuilder: (context) => _buildNormalRow(context),
      editModeBuilder: (context) => _buildEditRow(context),
      showDivider:
          false, // Don't show divider in SwipeableItem to avoid double borders
    );
  }

  /// Build the context menu that appears when swiped
  Widget _buildContextMenu(BuildContext context, double totalMenuWidth) {
    // Get the right padding from the main row
    const double rightPadding = 16.0;
    const double iconWidth = 40.0; // Width of each icon

    return Container(
      width: totalMenuWidth,
      height: double.infinity, // Make sure it stretches to full height
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment:
            CrossAxisAlignment.center, // Center icons vertically
        children: [
          // Delete icon (only icon in the menu now)
          SizedBox(
            width: iconWidth,
            child: Center(
              child: IconButton(
                icon: Icon(
                  Icons.delete,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                  size: 24,
                ),
                onPressed: () {
                  // Show confirmation dialog before deleting
                  _showDeleteConfirmationDialog(context);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ),

          // Right padding
          SizedBox(width: rightPadding),
        ],
      ),
    );
  }

  /// Build the normal row display for the idea
  Widget _buildNormalRow(BuildContext context) {
    // Format the creation date in local timezone
    final localCreatedAt = widget.idea.createdAt.toLocal();
    final formattedDate = DateFormat('MMM d, yyyy').format(localCreatedAt);

    // Log the current idea content for debugging
    Logger.debug('_buildNormalRow - Idea ID: ${widget.idea.id}, isDone: ${widget.idea.isDone}');
    Logger.debug(
      '_buildNormalRow - Content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
    );

    // Check if this idea is in swipe mode
    final swipedId = ref.watch(swipedIdeaIdProvider);
    final isSwiped = swipedId == widget.idea.id;

    // Get ideabook data to check if we're in todo mode (using stream provider for reactive updates)
    final ideabookAsync = ref.watch(ideabookStreamProvider(widget.ideabookId));
    final isInTodoMode = ideabookAsync.maybeWhen(
      data: (ideabook) {
        final todoMode = ideabook?.showAsTodoList ?? false;
        Logger.debug('SwipeableIdeaItem: Ideabook ${widget.ideabookId} todo mode: $todoMode (ideabook: ${ideabook?.toString()})');
        return todoMode;
      },
      orElse: () {
        Logger.debug('SwipeableIdeaItem: Ideabook ${widget.ideabookId} not loaded or error');
        return false;
      },
    );

    // Determine the display state of the idea
    Widget contentWidget;
    VoidCallback? onTapAction;

    if (widget.idea.hasTranscriptionError) {
      // Failed transcription state - show retry option
      contentWidget = _buildFailedState(context, formattedDate);
      onTapAction = () => _handleRetryTranscription();
    } else if (widget.idea.isTranscribing) {
      // In-process transcription state - show transcribing animation
      contentWidget = _buildTranscribingState(context, formattedDate);
      onTapAction = null; // No action while transcribing
    } else if (widget.idea.isEmpty) {
      // Empty idea state
      contentWidget = _buildEmptyState(context, formattedDate);
      onTapAction = () => _handleEmptyIdeaTap(isSwiped);
    } else if (widget.idea.isCompleted) {
      // Regular completed idea
      contentWidget = _buildCompletedState(context, formattedDate);
      onTapAction = () => _handleCompletedIdeaTap(isSwiped);
    } else {
      // Fallback to regular display
      contentWidget = _buildCompletedState(context, formattedDate);
      onTapAction = () => _handleCompletedIdeaTap(isSwiped);
    }

    // If in todo mode, include checkbox in the layout
    if (isInTodoMode) {
      // Check for optimistic completion state first, fall back to actual state
      final optimisticStates = ref.watch(optimisticCompletionProvider);
      final isCompleted = optimisticStates[widget.idea.id] ?? widget.idea.isDone ?? false;
      Logger.debug('SwipeableIdeaItem: Rendering TODO mode UI for idea ${widget.idea.id}, isCompleted: $isCompleted');
      
      return InkWell(
        onTap: onTapAction,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Checkbox for todo completion
                Padding(
                  padding: const EdgeInsets.only(right: 12.0, top: 2.0),
                  child: GestureDetector(
                    onTap: () => _toggleIdeaCompletion(),
                    child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isCompleted 
                                  ? NoejiTheme.colorsOf(context).textPrimary
                                  : NoejiTheme.colorsOf(context).textSecondary,
                              width: 1,
                            ),
                            color: isCompleted 
                                ? NoejiTheme.colorsOf(context).textPrimary
                                : Colors.transparent,
                          ),
                          child: isCompleted
                              ? Icon(
                                  Icons.check,
                                  size: 14,
                                  color: Theme.of(context).scaffoldBackgroundColor,
                                )
                              : null,
                        ),
                      ),
                    ),
                
                // Content with proper opacity for completed items
                Expanded(
                  child: Opacity(
                    opacity: isCompleted ? 0.5 : 1.0,
                    child: contentWidget,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      // Regular layout when not in todo mode
      Logger.debug('SwipeableIdeaItem: Rendering REGULAR mode UI for idea ${widget.idea.id}');
      return InkWell(
        onTap: onTapAction,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.all(12.0), // Reduced padding
            child: contentWidget,
          ),
        ),
      );
    }
  }

  /// Build the display for a completed regular idea
  Widget _buildCompletedState(BuildContext context, String formattedDate) {
    // Check if this idea is completed in todo mode for strikethrough formatting
    final ideabookAsync = ref.watch(ideabookStreamProvider(widget.ideabookId));
    final isInTodoMode = ideabookAsync.maybeWhen(
      data: (ideabook) => ideabook?.showAsTodoList ?? false,
      orElse: () => false,
    );
    
    // Check for optimistic completion state first, fall back to actual state
    final optimisticStates = ref.watch(optimisticCompletionProvider);
    final isCompleted = optimisticStates[widget.idea.id] ?? widget.idea.isDone ?? false;
    
    // Check for optimistic edit content first, fall back to actual content
    final optimisticEdits = ref.watch(optimisticEditContentProvider);
    final displayContent = optimisticEdits[widget.idea.id] ?? widget.idea.content;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Idea content with markdown rendering and strikethrough if completed in todo mode
        _buildMarkdownContent(
          context,
          displayContent,
          isInTodoMode && isCompleted,
        ),
        // Date
        const SizedBox(height: 4),
        Text(
          formattedDate,
          style: NoejiTheme.textStylesOf(context).bodySmall,
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  /// Build markdown content widget with optional strikethrough for completed todos
  Widget _buildMarkdownContent(BuildContext context, String content, bool isCompleted) {
    return MarkdownBody(
      data: content,
      onTapLink: (text, href, title) async {
        if (href != null) {
          final uri = Uri.tryParse(href);
          if (uri != null && await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          }
        }
      },
      styleSheet: MarkdownStyleSheet(
        p: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        h1: NoejiTheme.textStylesOf(context).titleLarge.copyWith(
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        h2: NoejiTheme.textStylesOf(context).titleMedium.copyWith(
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        h3: NoejiTheme.textStylesOf(context).titleSmall.copyWith(
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        strong: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
          fontWeight: FontWeight.bold,
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        em: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
          fontStyle: FontStyle.italic,
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        code: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
          fontFamily: 'monospace',
          backgroundColor: NoejiTheme.colorsOf(context).textSecondary.withValues(alpha: 0.1),
          decoration: isCompleted 
              ? TextDecoration.lineThrough 
              : TextDecoration.none,
          decorationColor: NoejiTheme.colorsOf(context).textSecondary,
        ),
        a: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
          color: Colors.blue,
        ),
      ),
      shrinkWrap: true,
      selectable: false,
    );
  }

  /// Build the display for an empty idea
  Widget _buildEmptyState(BuildContext context, String formattedDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Empty idea placeholder
        Text(
          'New Idea',
          style: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
            fontStyle: FontStyle.italic,
            color: NoejiTheme.colorsOf(context).textSecondary,
          ),
          textAlign: TextAlign.left,
        ),
        // Date
        const SizedBox(height: 4),
        Text(
          formattedDate,
          style: NoejiTheme.textStylesOf(context).bodySmall,
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  /// Build the display for a transcribing idea with animation
  Widget _buildTranscribingState(BuildContext context, String formattedDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Transcribing text with animated dots
        Row(
          children: [
            Text(
              'Transcribing',
              style: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
                color: NoejiTheme.colorsOf(context).textSecondary,
              ),
              textAlign: TextAlign.left,
            ),
            _buildAnimatedDots(context),
          ],
        ),
        // Date
        const SizedBox(height: 4),
        Text(
          formattedDate,
          style: NoejiTheme.textStylesOf(context).bodySmall,
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  /// Build the display for a failed transcription with retry option
  Widget _buildFailedState(BuildContext context, String formattedDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Failed transcription message
        Text(
          'Transcription failed. Tap to retry.',
          style: NoejiTheme.textStylesOf(
            context,
          ).bodyMedium.copyWith(color: Colors.red),
          textAlign: TextAlign.left,
        ),
        // Date
        const SizedBox(height: 4),
        Text(
          formattedDate,
          style: NoejiTheme.textStylesOf(context).bodySmall,
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  /// Build animated dots for transcribing state
  Widget _buildAnimatedDots(BuildContext context) {
    return _AnimatedDots();
  }

  /// Handle tap for completed ideas
  void _handleCompletedIdeaTap(bool isSwiped) {
    if (isSwiped) {
      // If swiped, close the context menu instead of entering edit mode
      ref.read(swipedIdeaIdProvider.notifier).state = null;
    } else {
      // Check if recording is active - if so, don't allow edit mode
      final panelState = ref.read(ideabookDetailBottomPanelStateProvider);
      final ideaEditRecording = ref.read(ideaEditRecordingProvider);
      final isRecording = panelState == IdeabookDetailBottomPanelState.recording || 
                         ideaEditRecording != null;
      
      if (isRecording) {
        // Don't enter edit mode when recording is active
        Logger.debug('Blocking edit mode entry - recording is active');
        return;
      }
      
      // Provide haptic feedback for entering edit mode
      HapticFeedbackService.trigger(HapticAction.editModeEnter);
      
      // Exit any existing placeholder mode and idea edit mode with auto-save before entering edit mode
      EditModeHelper.exitPlaceholderModeWithAutoSave(ref)
          .then((_) {
            return EditModeHelper.exitIdeaEditModeWithAutoSave(
              ref,
              excludeIdeaId: widget.idea.id,
            );
          })
          .then((_) {
            // Enter edit mode when tapping anywhere on the row
            _resetToOriginalContent();
            // Reset the focus flag when entering edit mode
            setState(() {
              _shouldFocusTextField = true;
            });

            // Update the edit state provider with current content
            ref.read(ideaEditStateProvider.notifier).state = IdeaEditState(
              currentEditIdeaId: widget.idea.id,
              currentEditContent: widget.idea.content,
              currentIdeabookId: widget.ideabookId,
            );

            ref.read(ideaEditProvider.notifier).state = widget.idea.id;
          });
    }
  }

  /// Handle tap for empty ideas
  void _handleEmptyIdeaTap(bool isSwiped) {
    if (isSwiped) {
      // If swiped, close the context menu
      ref.read(swipedIdeaIdProvider.notifier).state = null;
    } else {
      // Check if recording is active - if so, don't allow edit mode
      final panelState = ref.read(ideabookDetailBottomPanelStateProvider);
      final ideaEditRecording = ref.read(ideaEditRecordingProvider);
      final isRecording = panelState == IdeabookDetailBottomPanelState.recording || 
                         ideaEditRecording != null;
      
      if (isRecording) {
        // Don't enter edit mode when recording is active
        Logger.debug('Blocking edit mode entry - recording is active');
        return;
      }
      
      // Provide haptic feedback for entering edit mode
      HapticFeedbackService.trigger(HapticAction.editModeEnter);
      
      // Exit any existing placeholder mode and idea edit mode with auto-save before entering edit mode
      EditModeHelper.exitPlaceholderModeWithAutoSave(ref)
          .then((_) {
            return EditModeHelper.exitIdeaEditModeWithAutoSave(
              ref,
              excludeIdeaId: widget.idea.id,
            );
          })
          .then((_) {
            // For empty ideas, also enter edit mode
            _resetToOriginalContent();
            setState(() {
              _shouldFocusTextField = true;
            });

            // Update the edit state provider with current content
            ref.read(ideaEditStateProvider.notifier).state = IdeaEditState(
              currentEditIdeaId: widget.idea.id,
              currentEditContent: widget.idea.content,
              currentIdeabookId: widget.ideabookId,
            );

            ref.read(ideaEditProvider.notifier).state = widget.idea.id;
          });
    }
  }

  /// Handle retry transcription for failed ideas
  void _handleRetryTranscription() async {
    try {
      // Import the background transcription service
      final backgroundTranscriptionService = ref.read(
        backgroundTranscriptionServiceProvider,
      );

      // Get ideabook name for the retry
      String? ideabookName;
      try {
        final listenerPool = ref.read(firestoreListenerPoolProvider);
        final ideabook =
            await listenerPool.getIdeabookStream(widget.ideabookId).first;
        if (ideabook != null) {
          ideabookName = ideabook.name;
        }
      } catch (e) {
        Logger.error('Error getting ideabook name for retry', e);
      }

      // Retry the transcription
      await backgroundTranscriptionService.retryTranscription(
        ideabookId: widget.ideabookId,
        ideaId: widget.idea.id,
        ideabookName: ideabookName,
      );

      Logger.debug('Transcription retry initiated for idea ${widget.idea.id}');

      // Show feedback to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Retrying transcription...'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      Logger.error(
        'Error retrying transcription for idea ${widget.idea.id}',
        e,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to retry transcription: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Build the edit row display
  Widget _buildEditRow(BuildContext context) {
    // Check if in recording mode
    final isRecording = ref.watch(ideaEditRecordingProvider) == widget.idea.id;
    final isEditing = ref.read(ideaEditProvider) == widget.idea.id;

    Logger.debug(
      '_buildEditRow - Idea ID: ${widget.idea.id}, isEditing: $isEditing',
    );
    Logger.debug(
      '_buildEditRow - Widget idea content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
    );
    Logger.debug(
      '_buildEditRow - Current text: "${_currentText.substring(0, _currentText.length.clamp(0, 20))}${_currentText.length > 20 ? "..." : ""}"',
    );
    Logger.debug(
      '_buildEditRow - Text controller: "${_textEditingController.text.substring(0, _textEditingController.text.length.clamp(0, 20))}${_textEditingController.text.length > 20 ? "..." : ""}"',
    );

    // Check if we just appended text - if so, don't reset the text controller
    if (_justAppendedText) {
      Logger.debug('Just appended text, not updating text controller');
      // Reset the flag for next time
      _justAppendedText = false;
    }
    // When entering edit mode, always ensure we have the original content from the widget
    else if (isEditing && _textEditingController.text != _currentText) {
      // We're in edit mode but the text controller doesn't match our current text
      // This happens when we've just entered edit mode
      Logger.debug('Updating text controller in edit mode');
      _textEditingController.text = _currentText;
      _textEditingController.selection = TextSelection.fromPosition(
        TextPosition(offset: _textEditingController.text.length),
      );
    }

    // Check for transcription result - use read instead of watch to avoid rebuild loops
    final transcription = ref.read(ideaEditTranscriptionProvider);
    if (transcription != null && transcription.ideaId == widget.idea.id) {
      Logger.debug(
        'Found transcription in _buildEditRow: ${transcription.transcribedText}',
      );
      // Schedule the append operation after the build is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _appendTranscribedText(transcription.transcribedText);
        // Reset the provider
        ref.read(ideaEditTranscriptionProvider.notifier).state = null;
      });
    }

    // Request focus when entering edit mode or after recording, but only if _shouldFocusTextField is true
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isEditing && _shouldFocusTextField) {
        _focusNode.requestFocus();
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text field for editing the content
        Align(
          alignment: Alignment.centerLeft,
          child: Container(
            // Calculate approximate height for 10 lines of text
            // This is an estimate based on the line height of bodyMedium text style
            constraints: const BoxConstraints(
              maxHeight: 240, // Approximate height for 10 lines (24px per line)
            ),
            child: SingleChildScrollView(
              child: TextField(
                key: ValueKey(
                  'idea_edit_${widget.idea.id}_${_currentText.length}_$_hasAppendedText',
                ),
                controller: _textEditingController,
                focusNode: _focusNode,
                style: NoejiTheme.textStylesOf(context).bodyMedium,
                textAlign: TextAlign.left,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                  alignLabelWithHint: true,
                ),
                maxLines: null, // Allow multiple lines
                textInputAction: TextInputAction.newline,
                // Always keep enabled, even during recording, to preserve cursor position
                enabled: true,
                // Don't save on Enter, just insert a newline
                onEditingComplete: () {
                  // Do nothing, let the newline be inserted
                },
                onChanged: (text) {
                  // Update our stored text when the user edits it directly
                  _currentText = text;
                },
                onTap: () {
                  // Reset the focus flag when user taps on the text field
                  if (!_shouldFocusTextField) {
                    setState(() {
                      _shouldFocusTextField = true;
                    });
                  }
                },
              ),
            ),
          ),
        ),

        // Date (not editable)
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            DateFormat('MMM d, yyyy').format(widget.idea.createdAt.toLocal()),
            style: NoejiTheme.textStylesOf(context).bodySmall,
            textAlign: TextAlign.left,
          ),
        ),

        // Buttons row or recording panel
        const SizedBox(height: 16),
        if (isRecording)
          // Recording panel
          _buildRecordingPanel(context)
        else
          // Normal buttons row
          _buildEditButtonsRow(context),
      ],
    );
  }

  /// Build the recording panel for appending text to the idea
  Widget _buildRecordingPanel(BuildContext context) {
    // Store a reference to the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    return AdvancedAudioRecordingPanel(
      layout: AudioRecordingPanelLayout.horizontal,
      showTimer: false,
      onRecordingCompleted: (filePath, duration) async {
        Logger.debug(
          'Idea edit recording completed: $filePath, duration: ${duration.inSeconds}s',
        );

        try {
          // Use the LLM service to transcribe the audio
          final llmService = ref.read(llmServiceProvider);
          final transcriptionResult = await llmService.transcribeAudio(
            filePath,
            useCase: TranscriptionUseCase.newIdea,
          );

          if (transcriptionResult.isSuccess &&
              transcriptionResult.idea != null) {
            // Get the transcribed text
            final transcribedText = transcriptionResult.idea!;
            Logger.debug('Transcription successful: $transcribedText');

            // Provide haptic feedback for transcription ready
            HapticFeedbackService.trigger(HapticAction.transcriptionReady);
            
            // If the widget is still mounted, directly append the text
            if (mounted) {
              _appendTranscribedText(transcribedText);
            } else {
              // If the widget is not mounted, store the transcription result in the provider
              // This will trigger the UI update when the widget is rebuilt
              Logger.debug(
                'Widget not mounted, storing transcription in provider',
              );
              ref
                  .read(ideaEditTranscriptionProvider.notifier)
                  .state = IdeaEditTranscription(
                ideaId: widget.idea.id,
                transcribedText: transcribedText,
              );
            }

            // Exit recording mode after successful transcription
            ref.read(ideaEditRecordingProvider.notifier).state = null;
          } else {
            // Handle transcription failure
            Logger.error(
              'Transcription failed: ${transcriptionResult.errorMessage}',
            );
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Transcription failed'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        } catch (e) {
          Logger.error('Error processing recording', e);
          if (mounted) {
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('Error processing recording: $e'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      },
      onRecordingCancelled: () {
        Logger.debug('Idea edit recording cancelled');
        // Exit recording mode
        ref.read(ideaEditRecordingProvider.notifier).state = null;
      },
      onRecordingFailed: (errorMessage) {
        Logger.error('Idea edit recording failed: $errorMessage');
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Recording failed: $errorMessage'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
        // Exit recording mode
        ref.read(ideaEditRecordingProvider.notifier).state = null;
      },
      onPermissionDenied: () {
        Logger.error('Idea edit recording permission denied');
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Microphone permission required'),
              duration: Duration(seconds: 2),
            ),
          );
        }
        // Exit recording mode
        ref.read(ideaEditRecordingProvider.notifier).state = null;
      },
    );
  }

  /// Build the edit buttons row (save/cancel/record buttons)
  Widget _buildEditButtonsRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Mic button (left-aligned)
        IconButton(
          onPressed: () => _startRecording(),
          icon: Icon(
            Icons.mic,
            color: NoejiTheme.colorsOf(context).textPrimary,
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),

        // Right-aligned buttons
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Cancel button
            TextButton(
              onPressed: () {
                // Provide haptic feedback for cancel action
                HapticFeedbackService.trigger(HapticAction.editModeCancel);
                // Reset to original text and exit edit mode
                _resetToOriginalContent();
                ref.read(ideaEditProvider.notifier).state = null;
              },
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(context).buttonText.copyWith(
                  color: NoejiTheme.colorsOf(context).textSecondary,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Save button
            TextButton(
              onPressed: () {
                // Provide haptic feedback for save action
                HapticFeedbackService.trigger(HapticAction.editModeSave);
                _saveChanges();
              },
              child: Text(
                'Save',
                style: NoejiTheme.textStylesOf(context).buttonText.copyWith(
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Start recording for appending text to the idea
  void _startRecording() async {
    // Provide haptic feedback for mic button press
    HapticFeedbackService.trigger(HapticAction.recordingStart);
    
    // Check if microphone permission is granted
    final audioRecordingService = ref.read(audioRecordingServiceProvider);
    final hasPermission = await audioRecordingService.checkPermission();

    if (hasPermission) {
      // Enter recording mode
      ref.read(ideaEditRecordingProvider.notifier).state = widget.idea.id;
      return;
    }

    // Request permission if not granted
    final status = await audioRecordingService.requestPermission();
    if (status.isGranted) {
      // Enter recording mode
      ref.read(ideaEditRecordingProvider.notifier).state = widget.idea.id;
    } else {
      // Show permission denied message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Microphone permission required for recording'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Auto-save changes to the idea (used when switching edit modes)
  void _autoSaveChanges() async {
    try {
      Logger.debug('Auto-saving idea ${widget.idea.id}');

      // Get the updated text
      final updatedText = _textEditingController.text.trim();

      // Check if there are actual changes
      if (updatedText == widget.idea.content.trim()) {
        // No changes, just reset to original content
        _resetToOriginalContent();
        return;
      }

      // Check word limit if applicable
      if (_maxIdeaWords != null) {
        final wordCount =
            updatedText.split(' ').where((word) => word.isNotEmpty).length;
        if (wordCount > _maxIdeaWords!) {
          Logger.debug('Auto-save cancelled: idea exceeds word limit');
          // Don't auto-save if it exceeds the limit, just reset
          _resetToOriginalContent();
          return;
        }
      }

      // Create updated idea
      final updatedIdea = widget.idea.copyWith(
        content: updatedText,
        updatedAt: DateTime.now(),
      );

      // Save to Firestore
      final firestoreService = ref.read(firestoreServiceProvider);
      final success = await firestoreService.updateIdea(
        widget.ideabookId,
        updatedIdea,
      );

      if (success) {
        // Update local state
        _resetToOriginalContent();
        Logger.debug('Auto-saved idea ${widget.idea.id} successfully');
      } else {
        Logger.error('Failed to auto-save idea ${widget.idea.id}');
        // Reset to original content anyway to avoid confusion
        _resetToOriginalContent();
      }

      // Clear the edit state since we're no longer editing
      ref.read(ideaEditStateProvider.notifier).state = const IdeaEditState();
    } catch (e) {
      Logger.error('Error auto-saving idea changes', e);
      // Reset to original content anyway to avoid confusion
      _resetToOriginalContent();
    }
  }

  /// Save changes to the idea with optimistic UI updates
  void _saveChanges() async {
    try {
      // Get the updated text
      final updatedText = _textEditingController.text.trim();

      // Check if there are actual changes
      if (updatedText == widget.idea.content.trim()) {
        // No changes, just exit edit mode
        ref.read(ideaEditProvider.notifier).state = null;
        return;
      }

      // Check word limit if applicable
      if (_maxIdeaWords != null) {
        final wordCount =
            updatedText.split(' ').where((word) => word.isNotEmpty).length;
        if (wordCount > _maxIdeaWords!) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Idea exceeds the $_maxIdeaWords word limit'),
                duration: const Duration(seconds: 3),
              ),
            );
          }
          return;
        }
      }

      // STEP 1: Immediate optimistic UI update
      final optimisticEdits = ref.read(optimisticEditContentProvider);
      ref.read(optimisticEditContentProvider.notifier).state = {
        ...optimisticEdits,
        widget.idea.id: updatedText,
      };

      // STEP 2: Exit edit mode immediately for better UX
      _resetToOriginalContent();
      ref.read(ideaEditStateProvider.notifier).state = const IdeaEditState();
      ref.read(ideaEditProvider.notifier).state = null;

      // STEP 3: Fire and forget Firestore update in background
      _performBackgroundFirestoreUpdate(updatedText);

    } catch (e) {
      Logger.error('Error in optimistic save setup', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving changes: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Perform the actual Firestore update in background (fire and forget)
  void _performBackgroundFirestoreUpdate(String updatedText) async {
    try {
      // Create updated idea
      final updatedIdea = widget.idea.copyWith(
        content: updatedText,
        updatedAt: DateTime.now(),
      );

      // Save to Firestore
      final firestoreService = ref.read(firestoreServiceProvider);
      final success = await firestoreService.updateIdea(
        widget.ideabookId,
        updatedIdea,
      );

      if (success) {
        // SUCCESS: Clear optimistic state since Firestore will now provide the real data
        final currentOptimisticEdits = ref.read(optimisticEditContentProvider);
        final updatedOptimisticEdits = Map<String, String>.from(currentOptimisticEdits);
        updatedOptimisticEdits.remove(widget.idea.id);
        ref.read(optimisticEditContentProvider.notifier).state = updatedOptimisticEdits;
        
        Logger.debug('Background Firestore update successful for idea ${widget.idea.id}');
      } else {
        // FAILURE: Keep optimistic state but show error - user can try again
        Logger.error('Background Firestore update failed for idea ${widget.idea.id}');
        
        // Don't revert optimistic state immediately to avoid jarring UX
        // Instead, let the user see their changes and potentially retry
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Update may not have saved completely'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      Logger.error('Error in background Firestore update for idea ${widget.idea.id}', e);
      
      // Don't revert optimistic state on network errors to avoid jarring UX
      // The optimistic state will eventually be cleaned up by successful updates or app restart
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Update may not have saved completely'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Reset the text controller to the original idea content
  void _resetToOriginalContent() {
    _currentText = widget.idea.content;
    _textEditingController.text = _currentText;
    _hasAppendedText = false;
    _justAppendedText = false;
  }

  /// Toggle the completion state of this idea in todo mode
  Future<void> _toggleIdeaCompletion() async {
    // Provide immediate haptic feedback for better UX
    HapticFeedbackService.trigger(HapticAction.editModeSave);
    
    final newCompletionState = !(widget.idea.isDone ?? false);
    
    Logger.debug(
      'Toggling idea completion for idea ${widget.idea.id}: ${widget.idea.isDone ?? false} -> $newCompletionState',
    );

    // Immediately update optimistic state for instant UI feedback
    final optimisticStates = ref.read(optimisticCompletionProvider);
    ref.read(optimisticCompletionProvider.notifier).state = {
      ...optimisticStates,
      widget.idea.id: newCompletionState,
    };

    try {
      // Toggle the isDone field
      final updatedIdea = widget.idea.copyWith(
        isDone: newCompletionState,
        updatedAt: DateTime.now(),
      );

      // Update the idea in Firestore in background
      final firestoreService = ref.read(firestoreServiceProvider);
      final success = await firestoreService.updateIdea(
        widget.ideabookId,
        updatedIdea,
      );

      if (success) {
        // Clear optimistic state since Firestore update succeeded
        final currentOptimisticStates = ref.read(optimisticCompletionProvider);
        final updatedStates = Map<String, bool>.from(currentOptimisticStates);
        updatedStates.remove(widget.idea.id);
        ref.read(optimisticCompletionProvider.notifier).state = updatedStates;
      } else {
        Logger.error('Failed to toggle idea completion for idea ${widget.idea.id}');
        
        // Revert optimistic state on failure
        final currentOptimisticStates = ref.read(optimisticCompletionProvider);
        final updatedStates = Map<String, bool>.from(currentOptimisticStates);
        updatedStates.remove(widget.idea.id);
        ref.read(optimisticCompletionProvider.notifier).state = updatedStates;
        
        // Show error feedback to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update idea'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      Logger.error('Error toggling idea completion', e);
      
      // Revert optimistic state on error
      final currentOptimisticStates = ref.read(optimisticCompletionProvider);
      final updatedStates = Map<String, bool>.from(currentOptimisticStates);
      updatedStates.remove(widget.idea.id);
      ref.read(optimisticCompletionProvider.notifier).state = updatedStates;
      
      // Show error feedback to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'Delete Idea',
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              'Are you sure you want to delete this idea? This action cannot be undone.',
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(
                  'Cancel',
                  style: NoejiTheme.textStylesOf(dialogContext).buttonText,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _deleteIdea();
                },
                child: Text(
                  'Delete',
                  style: NoejiTheme.textStylesOf(
                    dialogContext,
                  ).buttonText.copyWith(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  /// Delete the idea
  void _deleteIdea() async {
    try {
      // Close any open context menu first
      ref.read(swipedIdeaIdProvider.notifier).state = null;

      // Delete from Firestore
      final firestoreService = ref.read(firestoreServiceProvider);
      final success = await firestoreService.deleteIdea(
        widget.ideabookId,
        widget.idea.id,
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Idea deleted'),
              duration: Duration(seconds: 1),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete idea'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      Logger.error('Error deleting idea', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting idea: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}

/// Stateful widget for animated dots to handle proper animation
class _AnimatedDots extends StatefulWidget {
  @override
  _AnimatedDotsState createState() => _AnimatedDotsState();
}

class _AnimatedDotsState extends State<_AnimatedDots>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final int dots = (_animationController.value * 4).floor() % 4;
        final String dotText = '.' * dots;
        return SizedBox(
          width: 20, // Fixed width to prevent layout shifts
          child: Text(
            dotText,
            style: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        );
      },
    );
  }
}
