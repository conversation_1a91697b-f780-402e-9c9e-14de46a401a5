import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:noeji/models/audio_recording_state.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/services/audio/audio_recording_controller.dart';
import 'package:noeji/ui/providers/audio_providers.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/common/reusable_waveform.dart';
import 'package:noeji/utils/logger.dart';

/// Layout options for the audio recording panel
enum AudioRecordingPanelLayout {
  /// Horizontal layout with buttons on the sides
  horizontal,

  /// Vertical layout with buttons at the bottom
  vertical,
}

/// Configuration for the audio recording panel
class AudioRecordingPanelConfig {
  /// The layout of the panel
  final AudioRecordingPanelLayout layout;

  /// Whether to show the timer
  final bool showTimer;

  /// The color of the waveform and buttons
  final Color? color;

  /// Whether to auto-start recording when the panel is shown
  final bool autoStart;

  /// Creates a new AudioRecordingPanelConfig
  const AudioRecordingPanelConfig({
    this.layout = AudioRecordingPanelLayout.horizontal,
    this.showTimer = false,
    this.color,
    this.autoStart = true,
  });

  /// Default configuration
  static AudioRecordingPanelConfig defaultConfig() {
    return const AudioRecordingPanelConfig(
      layout: AudioRecordingPanelLayout.horizontal,
      showTimer: false,
      autoStart: true,
    );
  }
}

/// A reusable audio recording panel that can be used in different contexts
class ReusableAudioRecordingPanel extends ConsumerStatefulWidget {
  /// The audio recording controller to use
  final BaseAudioRecordingController controller;

  /// Configuration for the panel
  final AudioRecordingPanelConfig config;

  /// Callback when recording is cancelled
  final VoidCallback? onCancel;

  /// Constructor
  const ReusableAudioRecordingPanel({
    super.key,
    required this.controller,
    this.config = const AudioRecordingPanelConfig(),
    this.onCancel,
  });

  @override
  ConsumerState<ReusableAudioRecordingPanel> createState() =>
      _ReusableAudioRecordingPanelState();
}

class _ReusableAudioRecordingPanelState
    extends ConsumerState<ReusableAudioRecordingPanel> {
  // Local processing state for this recording instance
  AudioProcessingState _localProcessingState = AudioProcessingState.idle;

  @override
  void dispose() {
    // Reset the global recording state when the widget is disposed
    // This ensures that the state doesn't persist when navigating away
    try {
      ref.read(audioRecordingStateProvider.notifier).resetState();
      Logger.debug(
        'ReusableAudioRecordingPanel: Reset recording state on dispose',
      );
    } catch (e) {
      Logger.debug(
        'ReusableAudioRecordingPanel: Could not reset recording state on dispose: $e',
      );
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    // Reset the recording state when the panel is initialized
    // This ensures that any previous recording state doesn't persist
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Reset the global recording state
      try {
        ref.read(audioRecordingStateProvider.notifier).resetState();
        Logger.debug(
          'ReusableAudioRecordingPanel: Reset recording state on initialization',
        );
      } catch (e) {
        Logger.debug(
          'ReusableAudioRecordingPanel: Could not reset recording state: $e',
        );
      }

      // Auto-start recording if configured
      if (widget.config.autoStart) {
        _startRecording();
      }
    });
  }

  /// Start recording
  Future<void> _startRecording() async {
    Logger.debug('ReusableAudioRecordingPanel: Starting recording');

    // Provide haptic feedback for recording start
    HapticFeedbackService.trigger(HapticAction.recordingStart);

    // Reset local processing state when starting a new recording
    setState(() {
      _localProcessingState = AudioProcessingState.idle;
    });

    // Also reset the global state to avoid interference from other recording instances
    try {
      // Reset the audio processing state
      ref.read(audioProcessingStateProvider.notifier).state =
          AudioProcessingState.idle;

      // Explicitly reset the recording state to ensure a clean start
      ref.read(audioRecordingStateProvider.notifier).resetState();
      Logger.debug(
        'ReusableAudioRecordingPanel: Reset recording state before starting',
      );
    } catch (e) {
      Logger.debug(
        'ReusableAudioRecordingPanel: Could not reset global state: $e',
      );
    }

    // Start recording using the controller
    // The controller will also reset the state again as an extra safeguard
    await widget.controller.startRecording(ref);
  }

  /// Complete recording
  Future<void> _completeRecording() async {
    Logger.debug('ReusableAudioRecordingPanel: Completing recording');

    // Provide haptic feedback for recording finish
    HapticFeedbackService.trigger(HapticAction.recordingFinish);

    // Get the current recording state
    final recordingState = ref.read(audioRecordingStateProvider);

    // Check if the recording is already completed (e.g., due to auto-stop)
    if (recordingState.state == RecordingState.completed) {
      Logger.debug(
        'ReusableAudioRecordingPanel: Recording already completed, processing it directly',
      );

      // Set local processing state to processing
      setState(() {
        _localProcessingState = AudioProcessingState.processing;
      });

      if (recordingState.filePath != null) {
        // Process the already completed recording
        await widget.controller.handleRecordingCompleted(
          ref,
          recordingState.filePath!,
          recordingState.duration,
        );
      } else {
        await widget.controller.handleRecordingFailed(
          ref,
          'No recording file path',
        );
      }

      // Reset local processing state after completion
      if (mounted) {
        setState(() {
          _localProcessingState = AudioProcessingState.completed;
        });
      }

      // Reset the global recording state after processing
      try {
        ref.read(audioRecordingStateProvider.notifier).resetState();
        Logger.debug(
          'ReusableAudioRecordingPanel: Reset recording state after completion (auto-stop case)',
        );
      } catch (e) {
        Logger.debug(
          'ReusableAudioRecordingPanel: Could not reset recording state: $e',
        );
      }

      return;
    }

    // Normal flow for active recording
    // Set local processing state to processing
    setState(() {
      _localProcessingState = AudioProcessingState.processing;
    });

    final filePath = widget.controller.getFilePath(ref);
    final duration = widget.controller.getDuration(ref);

    if (filePath != null) {
      final success = await widget.controller.stopRecording(ref);

      if (success) {
        await widget.controller.handleRecordingCompleted(
          ref,
          filePath,
          duration,
        );
      } else {
        await widget.controller.handleRecordingFailed(
          ref,
          'Failed to stop recording',
        );
      }
    } else {
      await widget.controller.handleRecordingFailed(
        ref,
        'No recording file path',
      );
    }

    // Reset local processing state after completion
    if (mounted) {
      setState(() {
        _localProcessingState = AudioProcessingState.completed;
      });
    }

    // Reset the global recording state after processing
    try {
      ref.read(audioRecordingStateProvider.notifier).resetState();
      Logger.debug(
        'ReusableAudioRecordingPanel: Reset recording state after completion (normal case)',
      );
    } catch (e) {
      Logger.debug(
        'ReusableAudioRecordingPanel: Could not reset recording state: $e',
      );
    }
  }

  /// Cancel recording
  Future<void> _cancelRecording() async {
    Logger.debug('ReusableAudioRecordingPanel: Cancelling recording');

    // Provide haptic feedback for recording cancel
    HapticFeedbackService.trigger(HapticAction.editModeCancel);

    await widget.controller.cancelRecording(ref);
    await widget.controller.handleRecordingCancelled(ref);

    // Call the onCancel callback if provided
    if (widget.onCancel != null) {
      widget.onCancel!();
    }

    // Reset local processing state
    if (mounted) {
      setState(() {
        _localProcessingState = AudioProcessingState.idle;
      });
    }

    // Reset the global recording state after cancellation
    try {
      ref.read(audioRecordingStateProvider.notifier).resetState();
      Logger.debug(
        'ReusableAudioRecordingPanel: Reset recording state after cancellation',
      );
    } catch (e) {
      Logger.debug(
        'ReusableAudioRecordingPanel: Could not reset recording state: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final recordingState = ref.watch(audioRecordingStateProvider);
    final amplitude = recordingState.amplitude;
    final duration = recordingState.duration;
    final isInCountdown = recordingState.isInCountdown;
    final countdownSeconds = recordingState.countdownSeconds;
    final themeColor =
        widget.config.color ?? NoejiTheme.colorsOf(context).textPrimary;

    // Check if the recording was auto-stopped due to time limit
    if (recordingState.autoStopped &&
        recordingState.state == RecordingState.completed) {
      // Handle auto-stopped recording
      Logger.debug(
        'ReusableAudioRecordingPanel: Detected auto-stopped recording, processing it',
      );

      // Use a microtask to avoid triggering during build
      Future.microtask(() async {
        if (recordingState.filePath != null) {
          // Store the file path and duration before processing
          final filePath = recordingState.filePath!;
          final duration = recordingState.duration;

          // Set local processing state to processing
          setState(() {
            _localProcessingState = AudioProcessingState.processing;
          });

          // Handle the recording completion
          await widget.controller.handleRecordingCompleted(
            ref,
            filePath,
            duration,
          );

          // Reset local processing state after completion
          if (mounted) {
            setState(() {
              _localProcessingState = AudioProcessingState.completed;
            });
          }

          // Reset the global recording state after processing
          try {
            ref.read(audioRecordingStateProvider.notifier).resetState();
            Logger.debug(
              'ReusableAudioRecordingPanel: Reset recording state after auto-stop processing',
            );
          } catch (e) {
            Logger.debug(
              'ReusableAudioRecordingPanel: Could not reset recording state: $e',
            );
          }
        }
      });
    }

    // Use local processing state instead of global state
    final processingState = _localProcessingState;

    // Build the appropriate layout
    return widget.config.layout == AudioRecordingPanelLayout.horizontal
        ? _buildHorizontalLayout(
          context,
          amplitude,
          duration,
          themeColor,
          processingState,
          isInCountdown,
          countdownSeconds,
        )
        : _buildVerticalLayout(
          context,
          amplitude,
          duration,
          themeColor,
          processingState,
          isInCountdown,
          countdownSeconds,
        );
  }

  /// Build horizontal layout with buttons on the sides
  Widget _buildHorizontalLayout(
    BuildContext context,
    double amplitude,
    Duration duration,
    Color themeColor,
    AudioProcessingState processingState,
    bool isInCountdown,
    int? countdownSeconds,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          // Cancel button
          IconButton(
            icon: Icon(Icons.close, color: themeColor),
            onPressed:
                processingState == AudioProcessingState.processing
                    ? null
                    : _cancelRecording,
          ),

          // Waveform visualization or countdown warning
          Expanded(
            child: ReusableWaveform(
              amplitude: amplitude,
              duration: duration,
              config: WaveformConfig(
                color: themeColor,
                showTimer: widget.config.showTimer,
                showCountdownWarning: isInCountdown,
                countdownSeconds: countdownSeconds,
              ),
            ),
          ),

          // Complete button or spinner
          processingState == AudioProcessingState.processing
              ? SizedBox(
                width: 48,
                height: 48,
                child: Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: themeColor,
                    ),
                  ),
                ),
              )
              : IconButton(
                icon: Icon(Icons.check, color: themeColor),
                onPressed: _completeRecording,
              ),
        ],
      ),
    );
  }

  /// Build vertical layout with buttons at the bottom
  Widget _buildVerticalLayout(
    BuildContext context,
    double amplitude,
    Duration duration,
    Color themeColor,
    AudioProcessingState processingState,
    bool isInCountdown,
    int? countdownSeconds,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Waveform visualization or countdown warning
          ReusableWaveform(
            amplitude: amplitude,
            duration: duration,
            config: WaveformConfig(
              color: themeColor,
              showTimer: widget.config.showTimer,
              height: 60,
              showCountdownWarning: isInCountdown,
              countdownSeconds: countdownSeconds,
            ),
          ),

          const SizedBox(height: 16),

          // Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Cancel button
              IconButton(
                icon: Icon(Icons.close, color: themeColor, size: 32),
                onPressed:
                    processingState == AudioProcessingState.processing
                        ? null
                        : _cancelRecording,
              ),

              // Complete button or spinner
              processingState == AudioProcessingState.processing
                  ? SizedBox(
                    width: 48,
                    height: 48,
                    child: Center(
                      child: SizedBox(
                        width: 32,
                        height: 32,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          color: themeColor,
                        ),
                      ),
                    ),
                  )
                  : IconButton(
                    icon: Icon(Icons.check, color: themeColor, size: 32),
                    onPressed: _completeRecording,
                  ),
            ],
          ),
        ],
      ),
    );
  }
}
