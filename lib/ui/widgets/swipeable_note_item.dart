import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:noeji/ui/providers/note_swipe_provider.dart';
import 'package:noeji/ui/screens/note_detail_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/common/swipeable_item.dart';

/// Widget for displaying a note item in the list with swipe functionality
class SwipeableNoteItem extends ConsumerWidget {
  /// The note to display
  final Note note;

  /// The ideabook this note belongs to
  final Ideabook ideabook;

  // Context menu constants
  static const double _maxSwipeExtent =
      60.0; // Maximum swipe distance for context menu (1 icon)
  static const double _swipeThreshold =
      40.0; // Threshold to determine when to complete the swipe

  /// Constructor
  const SwipeableNoteItem({
    super.key,
    required this.note,
    required this.ideabook,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the generic SwipeableItem widget
    return SwipeableItem(
      itemId: note.id,
      swipedItemProvider: swipedNoteIdProvider,
      maxSwipeExtent: _maxSwipeExtent,
      swipeThreshold: _swipeThreshold,
      contextMenuBuilder:
          (context, totalMenuWidth) =>
              _buildContextMenu(context, ref, totalMenuWidth),
      contentBuilder: (context) => _buildNormalRow(context),
      showDivider:
          false, // Don't show divider in SwipeableItem to avoid double borders
    );
  }

  /// Build the context menu that appears when swiped
  Widget _buildContextMenu(
    BuildContext context,
    WidgetRef ref,
    double totalMenuWidth,
  ) {
    // Constants for the context menu
    const double iconWidth = 60.0;

    return Container(
      width: totalMenuWidth,
      height: double.infinity, // Make sure it stretches to full height
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment:
            CrossAxisAlignment.center, // Center icons vertically
        children: [
          // Delete icon
          SizedBox(
            width: iconWidth,
            child: Center(
              child: IconButton(
                icon: Icon(
                  Icons.delete,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                  size: 24,
                ),
                onPressed: () {
                  // Show confirmation dialog before deleting
                  _showDeleteConfirmationDialog(context, ref);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the normal row content
  Widget _buildNormalRow(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        // Check if this note is in swipe mode
        final swipedId = ref.watch(swipedNoteIdProvider);
        final isSwiped = swipedId == note.id;

        return InkWell(
          onTap: () {
            if (isSwiped) {
              // If swiped, close the context menu instead of navigating
              ref.read(swipedNoteIdProvider.notifier).state = null;
            } else {
              // Provide strong haptic feedback when navigating to note detail
              HapticFeedbackService.trigger(HapticAction.itemSelect);

              // Navigate to the note detail screen as a separate page
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder:
                      (context) =>
                          NoteDetailScreen(note: note, ideabookId: ideabook.id),
                ),
              );
            }
          },
          child: Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Chat icon to indicate this is a note prompt
                Icon(
                  Icons.chat_bubble_outline,
                  size: 20.0,
                  color: NoejiTheme.colorsOf(context).textSecondary,
                ),

                // Space between icon and text
                const SizedBox(width: 12.0),

                // Note title (prompt) with expanded width
                Expanded(
                  child: Text(
                    note.title,
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Show confirmation dialog before deleting a note
  void _showDeleteConfirmationDialog(BuildContext context, WidgetRef ref) {
    // Store the scaffold messenger before the async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Get appropriate text style for snackbar
    final snackBarTextColor = NoejiTheme.getSnackBarTextColor(context);
    final textStyle = NoejiTheme.textStylesOf(
      context,
    ).bodyMedium.copyWith(color: snackBarTextColor);

    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'Delete Note',
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              'Are you sure you want to delete this note?',
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(false),
                child: Text(
                  'Cancel',
                  style: NoejiTheme.textStylesOf(dialogContext).buttonText,
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(true),
                child: Text(
                  'Delete',
                  style: NoejiTheme.textStylesOf(
                    dialogContext,
                  ).buttonText.copyWith(
                    color: NoejiTheme.colorsOf(dialogContext).error,
                  ),
                ),
              ),
            ],
          ),
    ).then((confirmDelete) {
      if (confirmDelete == true) {
        // Delete the note directly using the repository
        final repository = ref.read(noteRepositoryProvider);
        repository.deleteNote(ideabook.id, note.id);

        // Show a snackbar to confirm deletion
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Note deleted', style: textStyle),
            duration: const Duration(seconds: 1),
          ),
        );
      }

      // Close the context menu
      ref.read(swipedNoteIdProvider.notifier).state = null;
    });
  }
}
