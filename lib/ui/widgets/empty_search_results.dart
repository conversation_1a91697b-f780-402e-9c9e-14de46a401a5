import 'package:flutter/material.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Widget to display when search results are empty
class EmptySearchResults extends StatelessWidget {
  /// The search query that returned no results
  final String searchQuery;

  /// Constructor
  const EmptySearchResults({super.key, required this.searchQuery});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: NoejiTheme.colorsOf(context).textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No ideas found',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'No ideas match "$searchQuery"',
            style: NoejiTheme.textStylesOf(context).bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}