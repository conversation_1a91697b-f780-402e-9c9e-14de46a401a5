import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/placeholder_idea_provider.dart';
import 'package:noeji/ui/providers/firestore_idea_provider.dart';
import 'package:noeji/ui/providers/idea_edit_provider.dart';
import 'package:noeji/ui/providers/idea_edit_recording_provider.dart';
import 'package:noeji/ui/providers/idea_edit_transcription_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/advanced_audio_recording_panel.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/llm/llm_providers.dart';
import 'package:noeji/services/llm/llm_service.dart';
import 'package:noeji/utils/logger.dart';

/// Widget for displaying and editing a placeholder idea
/// Matches the exact UI of regular idea edit mode
class PlaceholderIdeaItem extends ConsumerStatefulWidget {
  /// The placeholder idea to display
  final PlaceholderIdea placeholder;

  /// Constructor
  const PlaceholderIdeaItem({super.key, required this.placeholder});

  @override
  ConsumerState<PlaceholderIdeaItem> createState() =>
      _PlaceholderIdeaItemState();
}

class _PlaceholderIdeaItemState extends ConsumerState<PlaceholderIdeaItem> {
  late TextEditingController _textController;
  late FocusNode _focusNode;

  // Cached max idea words limit
  int? _maxIdeaWords;

  // Flag to track if we've appended text
  bool _hasAppendedText = false;
  bool _justAppendedText = false;
  bool _shouldFocusTextField = true;

  // Use a unique placeholder ID for recording provider
  String get _placeholderId => 'placeholder_${widget.placeholder.ideabookId}';

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.placeholder.content);
    _focusNode = FocusNode();

    // Initialize the max idea words limit
    _initializeMaxIdeaWords();

    // Focus the text field immediately
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _focusNode.requestFocus();
      }
    });

    // Listen to text changes to update the provider
    _textController.addListener(() {
      PlaceholderIdeaHelper.updateContent(ref, _textController.text);
    });

    // Add a listener to the transcription provider
    Future.microtask(() {
      ref.listenManual(ideaEditTranscriptionProvider, (previous, next) {
        if (next != null && next.ideaId == _placeholderId) {
          Logger.debug(
            'Transcription provider updated in listener: ${next.transcribedText}',
          );
          _appendTranscribedText(next.transcribedText);
          // Reset the provider
          ref.read(ideaEditTranscriptionProvider.notifier).state = null;
        }
      });
    });
  }

  /// Initialize the maximum idea words limit from the provider
  Future<void> _initializeMaxIdeaWords() async {
    try {
      _maxIdeaWords = await ref.read(ideaMaxWordsProvider.future);
      Logger.debug(
        'PlaceholderIdeaItem: Max idea words initialized: $_maxIdeaWords',
      );
    } catch (e) {
      Logger.error(
        'PlaceholderIdeaItem: Failed to get max idea words, using fallback',
        e,
      );
      _maxIdeaWords = 1000; // Fallback to free tier limit
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// Insert transcribed text at the current cursor position
  void _appendTranscribedText(String transcribedText) {
    Logger.debug(
      'Inserting transcribed text at cursor position for placeholder',
    );
    Logger.debug('Current text length: ${_textController.text.length}');
    Logger.debug('Transcribed text length: ${transcribedText.length}');

    // Get the current text and cursor position
    final currentText = _textController.text;
    final selection = _textController.selection;

    // Determine where to insert the transcribed text
    String newText;
    int newCursorPosition;

    if (selection.isValid) {
      // Insert at cursor position
      final beforeCursor = currentText.substring(0, selection.start);
      final afterCursor = currentText.substring(selection.end);
      newText = '$beforeCursor$transcribedText$afterCursor';
      newCursorPosition = selection.start + transcribedText.length;
    } else {
      // Append to the end
      newText = '$currentText$transcribedText';
      newCursorPosition = newText.length;
    }

    _hasAppendedText = true;
    _justAppendedText = true;

    // Update the text controller
    _textController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.fromPosition(
        TextPosition(offset: newCursorPosition),
      ),
    );

    // Update the provider
    PlaceholderIdeaHelper.updateContent(ref, newText);

    // Force a rebuild
    setState(() {
      Logger.debug('Forcing UI update after inserting text at cursor position');
    });

    // Re-focus the text field to allow immediate editing
    _focusNode.requestFocus();

    Logger.debug(
      'Inserted transcribed text at cursor position for placeholder',
    );
  }

  /// Handle saving the placeholder idea
  Future<void> _handleSave() async {
    final content = _textController.text.trim();

    if (content.isEmpty) {
      // Show error for empty content
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Idea cannot be empty'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Check word limit
    final maxWords = _maxIdeaWords ?? 1000;
    if (content.length > maxWords) {
      // Show error dialog
      _showContentTooLongDialog(context, maxWords);
      return;
    }

    // Start the createIdea operation but DO NOT await it.
    // This sends the request to Firestore and moves on.
    ref
        .read(
          firestoreIdeasNotifierProvider(
            widget.placeholder.ideabookId,
          ).notifier,
        )
        .createIdea(content: content)
        .catchError((e) {
          // Optional: Catch potential errors from the background operation
          Logger.error('Background save failed for placeholder idea', e);
          // Since the UI is already gone, we could implement global error handling here
          // Return a fake idea as fallback - this won't be used since it's fire-and-forget
          return Idea(
            id: 'error',
            content: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
        });

    // Immediately remove the placeholder from the UI.
    // This makes the edit row disappear instantly.
    PlaceholderIdeaHelper.cancelPlaceholder(ref);

    // Show optimistic success message. The user sees this while
    // the new row is being added by the Firestore listener.
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Idea saved'),
          duration: Duration(seconds: 1),
        ),
      );
    }

    Logger.debug('Placeholder idea save initiated and UI removed.');
  }

  /// Auto-save the placeholder idea (used when switching to edit existing idea)
  void _autoSavePlaceholder() {
    final content = _textController.text.trim();

    if (content.isEmpty) {
      // No content to save, just cancel the placeholder
      PlaceholderIdeaHelper.cancelPlaceholder(ref);
      Logger.debug('Auto-cancelled empty placeholder');
      return;
    }

    // Check word limit
    final maxWords = _maxIdeaWords ?? 1000;
    if (content.length > maxWords) {
      // Content is too long, just cancel to avoid issues
      PlaceholderIdeaHelper.cancelPlaceholder(ref);
      Logger.debug('Auto-cancelled placeholder: content too long');
      return;
    }

    Logger.debug(
      'Auto-saving placeholder with content: "${content.substring(0, content.length.clamp(0, 50))}${content.length > 50 ? "..." : ""}"',
    );

    // Start the createIdea operation (fire-and-forget)
    ref
        .read(
          firestoreIdeasNotifierProvider(
            widget.placeholder.ideabookId,
          ).notifier,
        )
        .createIdea(content: content)
        .catchError((e) {
          Logger.error(
            'Background save failed for auto-saved placeholder idea',
            e,
          );
          // Return a fake idea as fallback - this won't be used since it's fire-and-forget
          return Idea(
            id: 'error',
            content: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
        });

    // Remove the placeholder from the UI immediately
    PlaceholderIdeaHelper.cancelPlaceholder(ref);
    Logger.debug('Auto-saved placeholder idea');
  }

  /// Handle canceling the placeholder idea
  void _handleCancel() {
    PlaceholderIdeaHelper.cancelPlaceholder(ref);
    Logger.debug('Placeholder idea cancelled');
  }

  /// Show a dialog when the idea content is too long
  Future<void> _showContentTooLongDialog(
    BuildContext context,
    int maxWords,
  ) async {
    // Set flag to prevent auto-focusing the text field
    setState(() {
      _shouldFocusTextField = false;
    });

    // Unfocus the text field to dismiss the keyboard
    _focusNode.unfocus();

    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Content Too Long! 📏',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            "This idea is over the $maxWords character limit. Shorten it a touch, and it's good to save! 👍",
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'OK',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build the recording panel for appending text to the placeholder
  Widget _buildRecordingPanel(BuildContext context) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    return AdvancedAudioRecordingPanel(
      layout: AudioRecordingPanelLayout.horizontal,
      showTimer: false,
      onRecordingCompleted: (filePath, duration) async {
        Logger.debug(
          'Placeholder recording completed: $filePath, duration: ${duration.inSeconds}s',
        );

        try {
          // Use the LLM service to transcribe the audio
          final llmService = ref.read(llmServiceProvider);
          final transcriptionResult = await llmService.transcribeAudio(
            filePath,
            useCase: TranscriptionUseCase.newIdea,
          );

          if (transcriptionResult.isSuccess &&
              transcriptionResult.idea != null) {
            // Get the transcribed text
            final transcribedText = transcriptionResult.idea!;
            Logger.debug('Transcription successful: $transcribedText');

            // If the widget is still mounted, directly append the text
            if (mounted) {
              _appendTranscribedText(transcribedText);
            } else {
              // If the widget is not mounted, store the transcription result in the provider
              Logger.debug(
                'Widget not mounted, storing transcription in provider',
              );
              ref
                  .read(ideaEditTranscriptionProvider.notifier)
                  .state = IdeaEditTranscription(
                ideaId: _placeholderId,
                transcribedText: transcribedText,
              );
            }
          } else {
            // Handle transcription failure
            Logger.error(
              'Transcription failed: ${transcriptionResult.errorMessage}',
            );
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Transcription failed'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        } catch (e) {
          Logger.error('Error processing recording', e);
          if (mounted) {
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('Error processing recording: $e'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } finally {
          // Exit recording mode
          if (mounted) {
            ref.read(ideaEditRecordingProvider.notifier).state = null;
          }
        }
      },
      onRecordingCancelled: () {
        Logger.debug('Placeholder recording cancelled');
        // Exit recording mode
        if (mounted) {
          ref.read(ideaEditRecordingProvider.notifier).state = null;
        }
      },
    );
  }

  /// Build the buttons row for the edit mode
  Widget _buildEditButtonsRow(BuildContext context) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Mic button (left-aligned)
        IconButton(
          icon: Icon(
            Icons.mic,
            color: NoejiTheme.colorsOf(context).textPrimary,
          ),
          onPressed: () async {
            // Check if microphone permission is granted
            final recordingService = ref.read(audioRecordingServiceProvider);
            final hasPermission = await recordingService.checkPermission();

            if (hasPermission) {
              // Enter recording mode
              if (mounted) {
                ref.read(ideaEditRecordingProvider.notifier).state =
                    _placeholderId;
              }
              return;
            }

            // Request permission if not granted
            final status = await recordingService.requestPermission();
            if (status.isGranted) {
              // Enter recording mode
              if (mounted) {
                ref.read(ideaEditRecordingProvider.notifier).state =
                    _placeholderId;
              }
            } else {
              // Show permission denied message
              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Microphone permission required'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            }
          },
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),

        // Right-aligned buttons
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Cancel button
            TextButton(
              onPressed: _handleCancel,
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Save button
            TextButton(
              onPressed: _handleSave,
              child: Text(
                'Save',
                style: TextStyle(
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Check if any existing idea has entered edit mode - if so, auto-save this placeholder
    final currentEditIdeaId = ref.watch(ideaEditProvider);
    if (currentEditIdeaId != null) {
      // An existing idea is entering edit mode, auto-save this placeholder
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _autoSavePlaceholder();
        }
      });
    }

    // Check if in recording mode
    final isRecording = ref.watch(ideaEditRecordingProvider) == _placeholderId;

    // Check for transcription result - use read instead of watch to avoid rebuild loops
    final transcription = ref.read(ideaEditTranscriptionProvider);
    if (transcription != null && transcription.ideaId == _placeholderId) {
      Logger.debug(
        'Found transcription in build: ${transcription.transcribedText}',
      );
      // Schedule the append operation after the build is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _appendTranscribedText(transcription.transcribedText);
        // Reset the provider
        ref.read(ideaEditTranscriptionProvider.notifier).state = null;
      });
    }

    // Check if we just appended text - if so, don't reset the text controller
    if (_justAppendedText) {
      Logger.debug('Just appended text, not updating text controller');
      // Reset the flag for next time
      _justAppendedText = false;
    }

    // Request focus when entering edit mode or after recording
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_shouldFocusTextField && mounted) {
        _focusNode.requestFocus();
      }
    });

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text field for editing the content (same style as regular edit mode)
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              // Calculate approximate height for 10 lines of text
              constraints: const BoxConstraints(
                maxHeight:
                    240, // Approximate height for 10 lines (24px per line)
              ),
              child: SingleChildScrollView(
                child: TextField(
                  key: ValueKey(
                    'placeholder_edit_${widget.placeholder.ideabookId}_$_hasAppendedText',
                  ),
                  controller: _textController,
                  focusNode: _focusNode,
                  style: NoejiTheme.textStylesOf(context).bodyMedium,
                  textAlign: TextAlign.left,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                    alignLabelWithHint: true,
                    hintText: 'Type your idea here...',
                    hintStyle: NoejiTheme.textStylesOf(
                      context,
                    ).bodyMedium.copyWith(
                      color: NoejiTheme.colorsOf(context).textSecondary,
                    ),
                  ),
                  maxLines: null, // Allow multiple lines
                  textInputAction: TextInputAction.newline,
                  enabled: true,
                  onEditingComplete: () {
                    // Do nothing, let the newline be inserted
                  },
                  onChanged: (text) {
                    // Update the provider when text changes
                    PlaceholderIdeaHelper.updateContent(ref, text);
                  },
                  onTap: () {
                    // Reset the focus flag when user taps on the text field
                    if (!_shouldFocusTextField) {
                      setState(() {
                        _shouldFocusTextField = true;
                      });
                    }
                  },
                ),
              ),
            ),
          ),

          // Date (current date since it's a new idea)
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              DateFormat('MMM d, yyyy').format(DateTime.now()),
              style: NoejiTheme.textStylesOf(context).bodySmall,
              textAlign: TextAlign.left,
            ),
          ),

          // Buttons row or recording panel
          const SizedBox(height: 16),
          if (isRecording)
            // Recording panel
            _buildRecordingPanel(context)
          else
            // Normal buttons row
            _buildEditButtonsRow(context),
        ],
      ),
    );
  }
}
