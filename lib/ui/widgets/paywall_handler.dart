import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/exceptions/limit_exceptions.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/paywall/paywall_providers.dart';
import 'package:noeji/services/paywall/paywall_trigger_service.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/screens/custom_paywall_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/thank_you_dialog.dart';
import 'package:noeji/utils/logger.dart';

/// Utility class for handling paywall triggers in the UI
class PaywallHandler {
  /// Handle a limit exception by showing either paywall or error dialog
  /// Returns true if the action should continue (user upgraded), false if cancelled
  static Future<bool> handleLimitException({
    required BuildContext context,
    required WidgetRef ref,
    required LimitException exception,
  }) async {
    try {
      final paywallTriggerService = ref.read(paywallTriggerServiceProvider);

      // Determine if we should show paywall or error dialog
      final result = await paywallTriggerService.shouldShowPaywallForLimit(
        limitType: exception.limitType,
        errorMessage: exception.message,
      );

      // Check if context is still valid
      if (!context.mounted) {
        Logger.debug('PaywallHandler: Context no longer mounted, aborting');
        return false;
      }

      if (result.shouldShowPaywall) {
        Logger.debug(
          'PaywallHandler: Showing paywall for ${exception.limitType}',
        );
        return await _showPaywall(context, ref, exception.limitType);
      } else {
        Logger.debug(
          'PaywallHandler: Showing error dialog for ${exception.limitType}',
        );
        await _showErrorDialog(
          context,
          result.errorMessage ?? exception.message,
        );
        return false;
      }
    } catch (e) {
      Logger.error('PaywallHandler: Error handling limit exception', e);
      // Fallback to error dialog
      if (context.mounted) {
        await _showErrorDialog(context, exception.message);
      }
      return false;
    }
  }

  /// Show the custom paywall for all users
  /// Returns true if user made a purchase, false if cancelled
  static Future<bool> _showPaywall(
    BuildContext context,
    WidgetRef ref,
    LimitType limitType,
  ) async {
    try {
      if (!context.mounted) return false;

      Logger.debug('PaywallHandler: Showing CustomPaywallScreen for all users');

      final purchased = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => const CustomPaywallScreen(),
          fullscreenDialog: true,
        ),
      );

      // CustomPaywallScreen returns true if user purchased, null/false if they didn't
      final didPurchase = purchased == true;
      Logger.debug('PaywallHandler: CustomPaywallScreen result: $didPurchase');

      if (didPurchase && context.mounted) {
        // Handle successful purchase - refresh user state and providers
        await _handleSuccessfulPurchase(context, ref);
      }

      return didPurchase;
    } catch (e) {
      Logger.error('PaywallHandler: Error showing paywall', e);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error showing subscription options. Please try again.',
              style: GoogleFonts.afacad(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// Handle successful purchase - refresh user state and show success message
  static Future<void> _handleSuccessfulPurchase(
    BuildContext context,
    WidgetRef ref,
  ) async {
    Logger.debug('PaywallHandler: User successfully purchased subscription');

    // Force refresh user tier immediately after purchase
    try {
      Logger.debug(
        'PaywallHandler: Refreshing user tier after successful purchase',
      );
      final refreshUserTier = ref.read(refreshUserTierProvider);
      await refreshUserTier();

      // Also refresh user state for the new 4-state model
      final refreshUserState = ref.read(refreshUserStateProvider);
      await refreshUserState();

      // Also invalidate all the old cached limit providers to force refresh
      Logger.debug('PaywallHandler: Invalidating cached limit providers');
      ref.invalidate(maxIdeabooksProvider);
      ref.invalidate(maxIdeasPerIdeabookProvider);
      ref.invalidate(maxNotesPerIdeabookProvider);
      ref.invalidate(chatRateLimitsProvider);
      ref.invalidate(userTierProvider);
      ref.invalidate(isProUserProvider);

      // Also invalidate real-time providers to force immediate UI updates
      Logger.debug('PaywallHandler: Invalidating real-time providers');
      ref.invalidate(userTierNotifierProvider);
      ref.invalidate(realtimeUserTierProvider);
      ref.invalidate(realtimeIsProUserProvider);
      ref.invalidate(userStateNotifierProvider);
      ref.invalidate(realtimeUserStateProvider);

      Logger.debug(
        'PaywallHandler: User tier and state refreshed successfully',
      );
    } catch (e) {
      Logger.error(
        'PaywallHandler: Failed to refresh user tier after purchase',
        e,
      );
      // Don't fail the purchase flow if refresh fails
    }

    // Show thank you dialog first (if not shown before)
    if (context.mounted) {
      await ThankYouDialog.showThankYouDialog(context);
    }

    // Show success message only after successful purchase and context is still mounted
    if (context.mounted) {
      Logger.debug('PaywallHandler: Showing welcome to pro notification');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Welcome to Noeji Pro! 🎉',
            style: GoogleFonts.afacad(),
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// Show error dialog for pro users or when paywall is not applicable
  static Future<void> _showErrorDialog(
    BuildContext context,
    String message,
  ) async {
    if (!context.mounted) return;

    return showDialog<void>(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              _getErrorTitle(message),
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              _getErrorContent(message),
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(
                  'OK',
                  style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                ),
              ),
            ],
          ),
    );
  }

  /// Get appropriate error title based on the message
  static String _getErrorTitle(String message) {
    if (message.toLowerCase().contains('ideabook')) {
      return 'Maximum Ideabooks Reached! 🈵';
    } else if (message.toLowerCase().contains('idea')) {
      return 'Ideabook Full! 📝';
    } else if (message.toLowerCase().contains('note')) {
      return 'Notes Limit Reached! 📋';
    } else if (message.toLowerCase().contains('chat')) {
      return 'Chat Limit Reached! 💬';
    }
    return 'Limit Reached!';
  }

  /// Get appropriate error content based on the message
  static String _getErrorContent(String message) {
    if (message.toLowerCase().contains('ideabook')) {
      return "Wow! You've hit the max ideabook milestone! 🏆 Amazing! To add more, you can free up space by deleting some you no longer need. 👍";
    } else if (message.toLowerCase().contains('idea')) {
      return "This ideabook is packed with ideas! 💡 To add more, consider deleting some existing ideas or creating a new ideabook.";
    } else if (message.toLowerCase().contains('note')) {
      return "This ideabook has reached its notes capacity! 📝 To add more notes, consider deleting some existing ones.";
    }
    return message;
  }
}
