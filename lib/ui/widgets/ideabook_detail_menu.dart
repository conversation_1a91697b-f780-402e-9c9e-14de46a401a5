import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/providers/ideabook_detail_menu_provider.dart';
import 'package:noeji/ui/providers/ideabook_provider.dart';
import 'package:noeji/ui/providers/search_bar_visibility_provider.dart';
import 'package:noeji/ui/providers/hide_completed_ideas_provider.dart';
import 'package:noeji/ui/providers/ideas_search_provider.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/services/export_service.dart';
import 'package:noeji/ui/providers/export_state_provider.dart';
import 'package:noeji/utils/file_export_util.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/screens/custom_paywall_screen.dart';
import 'package:noeji/ui/widgets/noeji_pro_logo.dart';

/// Menu for the ideabook detail page
class IdeabookDetailMenu extends ConsumerWidget {
  /// The ideabook ID
  final String ideabookId;

  /// Callback to switch to the Ideas tab
  final VoidCallback? onSwitchToIdeasTab;

  /// Constructor
  const IdeabookDetailMenu({
    super.key,
    required this.ideabookId,
    this.onSwitchToIdeasTab,
  });

  /// Show the search bar and focus on it
  Future<void> _showSearchBarAndFocus(
    BuildContext context,
    WidgetRef ref,
  ) async {
    // Provide immediate haptic feedback for better UX
    HapticFeedbackService.trigger(HapticAction.editModeSave);

    try {
      // Show the search bar if not already visible
      await ref.read(searchBarVisibilityProvider.notifier).setVisibility(true);
      
      // Trigger focus on the search bar
      ref.read(searchBarFocusTriggerProvider.notifier).state = true;
    } catch (e) {
      Logger.error('Error showing search bar', e);

      // Show error feedback to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Toggle the hide completed ideas preference
  Future<void> _toggleHideCompletedIdeas(
    BuildContext context,
    WidgetRef ref,
  ) async {
    // Provide immediate haptic feedback for better UX
    HapticFeedbackService.trigger(HapticAction.editModeSave);

    try {
      // Toggle the hide completed ideas preference
      await ref.read(hideCompletedIdeasProvider.notifier).toggle();
    } catch (e) {
      Logger.error('Error toggling hide completed ideas', e);

      // Show error feedback to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Toggle the todo list mode for the ideabook
  Future<void> _toggleTodoListMode(
    BuildContext context,
    WidgetRef ref,
    Ideabook ideabook,
  ) async {
    // Provide immediate haptic feedback for better UX
    HapticFeedbackService.trigger(HapticAction.editModeSave);

    final newTodoState = !ideabook.showAsTodoList;

    Logger.debug(
      'Toggling todo list mode for ideabook ${ideabook.id}: ${ideabook.showAsTodoList} -> $newTodoState',
    );

    try {
      // Toggle the showAsTodoList field
      final updatedIdeabook = ideabook.copyWith(
        showAsTodoList: newTodoState,
        updatedAt: DateTime.now(),
      );

      // Update the ideabook - UI will update automatically via reactive providers
      final ideabookRepository = ref.read(ideabookRepositoryProvider);
      final success = await ideabookRepository.updateIdeabook(updatedIdeabook);

      if (!success) {
        Logger.error(
          'Failed to toggle todo list mode for ideabook ${ideabook.id}',
        );

        // Show error feedback to user
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update todo list mode'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
      // Note: No need to log success or update UI here - the reactive providers handle UI updates
    } catch (e) {
      Logger.error('Error toggling todo list mode', e);

      // Show error feedback to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the ideabook data using stream provider for reactive updates
    final ideabookAsync = ref.watch(ideabookStreamProvider(ideabookId));
    
    // Watch the global search bar visibility preference
    final showSearchBar = ref.watch(searchBarVisibilityProvider);
    
    // Watch the hide completed ideas preference
    final hideCompletedIdeas = ref.watch(hideCompletedIdeasProvider);

    return Container(
      width: 310,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      child: ideabookAsync.when(
        loading:
            () => const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
        error:
            (error, _) => Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Error loading ideabook',
                style: NoejiTheme.textStylesOf(context).bodyMedium,
              ),
            ),
        data: (ideabook) {
          if (ideabook == null) {
            Logger.debug('Menu: Ideabook is null for ID $ideabookId');
            return Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Ideabook not found',
                style: NoejiTheme.textStylesOf(context).bodyMedium,
              ),
            );
          }

          Logger.debug(
            'Menu: Ideabook state - ID: ${ideabook.id}, showAsTodoList: ${ideabook.showAsTodoList}',
          );

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Find Ideas Menu Item
              ListTile(
                leading: Icon(
                  Icons.search,
                  color: Theme.of(context).iconTheme.color,
                ),
                title: Text(
                  'Find Ideas',
                  style: NoejiTheme.textStylesOf(context).bodyMedium,
                ),
                onTap: () async {
                  Logger.debug(
                    'Menu: User tapped Find Ideas. Current search bar state: $showSearchBar',
                  );

                  // Reset menu state and close the menu
                  ref.read(isDetailMenuOpenProvider.notifier).state = false;
                  Navigator.of(context).pop();

                  // Show the search bar and focus on it
                  await _showSearchBarAndFocus(context, ref);
                },
              ),

              // Todo List Mode Toggle
              ListTile(
                leading: Icon(
                  Icons.checklist,
                  color: Theme.of(context).iconTheme.color,
                ),
                title: Text(
                  'Show Ideas as To-Do List',
                  style: NoejiTheme.textStylesOf(context).bodyMedium,
                ),
                trailing: Icon(
                  ideabook.showAsTodoList
                      ? Icons.check_box
                      : Icons.check_box_outline_blank,
                  color: Theme.of(context).iconTheme.color,
                  size: 18,
                ),
                onTap: () async {
                  Logger.debug(
                    'Menu: User tapped toggle. Current state: ${ideabook.showAsTodoList}',
                  );

                  // Reset menu state and close the menu
                  ref.read(isDetailMenuOpenProvider.notifier).state = false;
                  Navigator.of(context).pop();

                  // Toggle the todo list mode
                  await _toggleTodoListMode(context, ref, ideabook);
                },
              ),

              // Hide Completed Ideas Toggle (only visible when in todo mode)
              if (ideabook.showAsTodoList)
                ListTile(
                  leading: Icon(
                    Icons.visibility_off,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  title: Text(
                    'Hide Completed Ideas',
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                  ),
                  trailing: Icon(
                    hideCompletedIdeas
                        ? Icons.check_box
                        : Icons.check_box_outline_blank,
                    color: Theme.of(context).iconTheme.color,
                    size: 18,
                  ),
                  onTap: () async {
                    Logger.debug(
                      'Menu: User tapped hide completed ideas toggle. Current state: $hideCompletedIdeas',
                    );

                    // Reset menu state and close the menu
                    ref.read(isDetailMenuOpenProvider.notifier).state = false;
                    Navigator.of(context).pop();

                    // Toggle the hide completed ideas preference
                    await _toggleHideCompletedIdeas(context, ref);
                  },
                ),

              // Divider before Export Ideas section
              Divider(
                height: 1,
                thickness: 1,
                color: NoejiTheme.colorsOf(context).divider,
              ),

              // Export Ideas section header
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Export Ideas',
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                  ),
                ),
              ),

              // Export Ideas as Markdown
              Consumer(
                builder: (context, ref, child) {
                  final exportState = ref.watch(exportStateProvider);
                  final isExportingMarkdown = exportState == ExportState.exportingIdeasMarkdown;
                  
                  return ListTile(
                    leading: isExportingMarkdown
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Icon(
                            Icons.article,
                            color: Theme.of(context).iconTheme.color,
                          ),
                    title: Text(
                      isExportingMarkdown ? 'Exporting...' : 'Export as Markdown',
                      style: NoejiTheme.textStylesOf(context).bodyMedium,
                    ),
                    enabled: exportState == ExportState.idle,
                    onTap: exportState == ExportState.idle ? () async {
                      // Reset menu state and close the menu
                      ref.read(isDetailMenuOpenProvider.notifier).state = false;
                      Navigator.of(context).pop();

                      // Show temporary notification
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Exporting as Markdown...'),
                            duration: Duration(days: 1), // Long duration until manually dismissed
                          ),
                        );
                      }

                      // Set exporting state
                      ref.read(exportStateProvider.notifier).state = ExportState.exportingIdeasMarkdown;

                      // Export ideas as markdown
                      try {
                        final exportService = ref.read(exportServiceProvider);
                        final markdownContent = await exportService.exportIdeasAsMarkdown(ideabookId);
                        
                        // Hide the temporary notification
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        }
                        
                        final success = await FileExportUtil.exportMarkdownFile(markdownContent, prefix: 'ideas');

                        if (success) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Ideas exported successfully'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        }
                        // Note: Don't show error for !success as it could be user cancellation
                      } catch (e) {
                        Logger.error('Export ideas as markdown failed: $e');
                        // Hide the temporary notification
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        }
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Failed to export as Markdown'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      } finally {
                        // Reset export state only if widget is still mounted
                        if (context.mounted) {
                          ref.read(exportStateProvider.notifier).state = ExportState.idle;
                        }
                      }
                    } : null,
                  );
                },
              ),

              // Export Ideas as HTML
              Consumer(
                builder: (context, ref, child) {
                  final exportState = ref.watch(exportStateProvider);
                  final isExportingHtml = exportState == ExportState.exportingIdeasHtml;
                  final isProUser = ref.watch(realtimeIsProUserProvider);
                  
                  if (isProUser) {
                    // Pro user - normal functionality
                    return ListTile(
                      leading: isExportingHtml
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Icon(
                              Icons.language,
                              color: Theme.of(context).iconTheme.color,
                            ),
                      title: Text(
                        isExportingHtml ? 'Exporting...' : 'Export as HTML',
                        style: NoejiTheme.textStylesOf(context).bodyMedium,
                      ),
                      enabled: exportState == ExportState.idle,
                      onTap: exportState == ExportState.idle ? () async {
                        // Reset menu state and close the menu
                        ref.read(isDetailMenuOpenProvider.notifier).state = false;
                        Navigator.of(context).pop();

                        // Show temporary notification
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Exporting as HTML...'),
                              duration: Duration(days: 1), // Long duration until manually dismissed
                            ),
                          );
                        }

                        // Set exporting state
                        ref.read(exportStateProvider.notifier).state = ExportState.exportingIdeasHtml;

                        // Export ideas as HTML
                        try {
                          final exportService = ref.read(exportServiceProvider);
                          final htmlContent = await exportService.exportIdeasAsHtml(ideabookId);
                          
                          // Hide the temporary notification
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).hideCurrentSnackBar();
                          }
                          
                          final success = await FileExportUtil.exportHtmlFile(htmlContent, prefix: 'ideas');
                          
                          if (success) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Ideas exported successfully'),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          }
                          // Note: Don't show error for !success as it could be user cancellation
                        } catch (e) {
                          Logger.error('Export ideas as HTML failed: $e');
                          // Hide the temporary notification
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).hideCurrentSnackBar();
                          }
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Failed to export as HTML'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        } finally {
                          // Reset export state only if widget is still mounted
                          if (context.mounted) {
                            ref.read(exportStateProvider.notifier).state = ExportState.idle;
                          }
                        }
                      } : null,
                    );
                  } else {
                    // Free user - entire area clickable to trigger paywall
                    return GestureDetector(
                      onTap: () {
                        // Reset menu state and close the menu
                        ref.read(isDetailMenuOpenProvider.notifier).state = false;
                        Navigator.of(context).pop();
                        
                        // Navigate to paywall
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const CustomPaywallScreen(),
                          ),
                        );
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Icon(
                                  Icons.language,
                                  color: Theme.of(context).disabledColor,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Export as HTML',
                                      style: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
                                        color: Theme.of(context).disabledColor,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Unlock with ',
                                          style: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
                                            color: Theme.of(context).hintColor,
                                          ),
                                        ),
                                        Transform.translate(
                                          offset: const Offset(0, 1),
                                          child: const NoejiProLogo(height: 14),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }
                },
              ),


            ],
          );
        },
      ),
    );
  }
}
