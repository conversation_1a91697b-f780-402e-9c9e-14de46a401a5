import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart' hide BottomPanelNotification;
import 'package:noeji/services/audio/idea_recording_controller.dart';
import 'package:noeji/ui/providers/bottom_panel_notification_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/advanced_audio_recording_panel.dart';

/// Enum representing the state of the ideabook detail bottom panel
enum IdeabookDetailBottomPanelState {
  /// Hidden state - panel is not visible
  hidden,

  /// Recording state showing the audio recording panel
  recording,

  /// Notification state showing a notification message
  notification,
}

/// Provider to track the state of the ideabook detail bottom panel
final ideabookDetailBottomPanelStateProvider =
    StateProvider<IdeabookDetailBottomPanelState>(
      (ref) => IdeabookDetailBottomPanelState.hidden,
    );

/// Widget that controls the bottom panel of the ideabook detail screen
class IdeabookDetailBottomPanelController extends ConsumerStatefulWidget {
  /// The ideabook ID
  final String ideabookId;

  /// Constructor
  const IdeabookDetailBottomPanelController({
    super.key,
    required this.ideabookId,
  });

  @override
  ConsumerState<IdeabookDetailBottomPanelController> createState() =>
      _IdeabookDetailBottomPanelControllerState();
}

class _IdeabookDetailBottomPanelControllerState
    extends ConsumerState<IdeabookDetailBottomPanelController> {
  @override
  Widget build(BuildContext context) {
    final panelState = ref.watch(ideabookDetailBottomPanelStateProvider);
    final notification = ref.watch(bottomPanelNotificationProvider);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: _buildPanel(context, panelState, notification),
    );
  }

  /// Build the appropriate panel based on the current state
  Widget _buildPanel(
    BuildContext context,
    IdeabookDetailBottomPanelState state,
    BottomPanelNotification? notification,
  ) {
    switch (state) {
      case IdeabookDetailBottomPanelState.hidden:
        return const SizedBox.shrink();
      case IdeabookDetailBottomPanelState.recording:
        return _buildRecordingPanel(context);
      case IdeabookDetailBottomPanelState.notification:
        if (notification != null) {
          return _buildNotificationPanel(context, notification);
        } else {
          // Fallback if notification is null but we're in notification state
          return const SizedBox.shrink();
        }
    }
  }

  // Fixed height for the bottom panel to maintain consistency across all states
  static const double kBottomPanelHeight = 80.0;

  /// Build the recording panel
  Widget _buildRecordingPanel(BuildContext context) {
    return SizedBox(
      height: kBottomPanelHeight,
      child: AdvancedAudioRecordingPanel(
        key: const ValueKey('recording-panel'),
        layout: AudioRecordingPanelLayout.horizontal,
        showTimer: false,
        onRecordingCompleted: _handleRecordingCompleted,
        onRecordingFailed: _handleRecordingFailed,
        onRecordingCancelled: _handleRecordingCancelled,
        onPermissionDenied: _handlePermissionDenied,
      ),
    );
  }

  /// Build the notification panel
  Widget _buildNotificationPanel(
    BuildContext context,
    BottomPanelNotification notification,
  ) {
    return SizedBox(
      key: const ValueKey('notification-panel'),
      height: kBottomPanelHeight,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Notification message
            Expanded(
              child: Text(
                notification.message,
                style: NoejiTheme.textStylesOf(
                  context,
                ).bodyLarge.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Check icon to indicate success
            Icon(
              Icons.check_circle,
              color: NoejiTheme.getIdeabookColor(
                context,
                IdeabookColor.green.index,
              ),
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  /// Handle recording completion
  Future<void> _handleRecordingCompleted(
    String filePath,
    Duration duration,
  ) async {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the recording completion
    await controller.handleRecordingCompleted(ref, filePath, duration);

    // Switch to notification state
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.notification;

    // Hide the panel after notification is shown
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
            IdeabookDetailBottomPanelState.hidden;
      }
    });
  }

  /// Handle recording cancellation
  void _handleRecordingCancelled() {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the recording cancellation
    controller.handleRecordingCancelled(ref);

    // Hide the panel
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.hidden;
  }

  /// Handle recording failure
  void _handleRecordingFailed(String errorMessage) {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the recording failure
    controller.handleRecordingFailed(ref, errorMessage);

    // Switch to notification state
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.notification;

    // Hide the panel after notification is shown
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
            IdeabookDetailBottomPanelState.hidden;
      }
    });
  }

  /// Handle permission denial
  void _handlePermissionDenied() {
    // Get the idea recording controller for this ideabook
    final controller = ref.read(
      ideaRecordingControllerProvider(widget.ideabookId),
    );

    // Handle the permission denial
    controller.handlePermissionDenied(ref);

    // Hide the panel
    ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
        IdeabookDetailBottomPanelState.hidden;
  }
}
