import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/combined_idea_provider.dart';
import 'package:noeji/ui/providers/firestore_idea_listener_controller.dart';
import 'package:noeji/ui/providers/placeholder_idea_provider.dart';
import 'package:noeji/ui/providers/idea_edit_provider.dart';
import 'package:noeji/ui/providers/idea_edit_recording_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/placeholder_idea_item.dart';
import 'package:noeji/ui/widgets/reorderable_ideas_list.dart';
import 'package:noeji/ui/widgets/ideabook_detail_bottom_panel_controller.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/exceptions/limit_exceptions.dart';
import 'package:noeji/ui/widgets/paywall_handler.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:noeji/utils/edit_mode_helper.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/ui/providers/ideas_search_provider.dart';
import 'package:noeji/ui/providers/ideabook_provider.dart';
import 'package:noeji/ui/widgets/ideas_search_bar.dart';
import 'package:noeji/ui/widgets/empty_search_results.dart';
import 'package:noeji/ui/providers/search_bar_visibility_provider.dart';
import 'package:noeji/ui/providers/hide_completed_ideas_provider.dart';

/// Widget for displaying the Ideas tab in the ideabook detail screen
class IdeasTab extends ConsumerStatefulWidget {
  /// The ideabook to display ideas for
  final Ideabook ideabook;

  /// Constructor
  const IdeasTab({super.key, required this.ideabook});

  @override
  ConsumerState<IdeasTab> createState() => _IdeasTabState();
}

class _IdeasTabState extends ConsumerState<IdeasTab> {
  // Store the controller reference as a class field
  FirestoreIdeasListenerController? _firestoreController;

  @override
  void initState() {
    super.initState();

    // Start listening to Firestore updates when the tab is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Logger.debug(
          'Starting Firestore ideas listener in IdeasTab for ideabook ${widget.ideabook.id}',
        );
        // Store the controller reference for later use in dispose
        _safelyUseRef((r) {
          _firestoreController = r.read(
            firestoreIdeasListenerControllerProvider(
              widget.ideabook.id,
            ).notifier,
          );
          _firestoreController?.startListening();
        });
      }
    });
  }

  @override
  void dispose() {
    // Don't stop the listener when the tab is hidden
    // The listener pool will handle TTL-based cleanup
    // Just clear the reference
    _firestoreController = null;

    // Safely clear any placeholder ideas for this ideabook when disposing
    _safelyUseRef((r) {
      final placeholder = r.read(placeholderIdeaProvider);
      if (placeholder != null && placeholder.ideabookId == widget.ideabook.id) {
        r.read(placeholderIdeaProvider.notifier).state = null;
      }
    });

    super.dispose();
  }

  /// Safely use ref to avoid errors when the widget is disposed
  T? _safelyUseRef<T>(T Function(WidgetRef) action, {T? defaultValue}) {
    try {
      return action(ref);
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug('IdeasTab: Could not use ref - widget may be disposed: $e');
      return defaultValue;
    }
  }

  @override
  void didUpdateWidget(IdeasTab oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the ideabook changed, clear any placeholder that doesn't belong to the new ideabook
    if (oldWidget.ideabook.id != widget.ideabook.id) {
      _safelyUseRef((r) {
        final placeholder = r.read(placeholderIdeaProvider);
        if (placeholder != null &&
            placeholder.ideabookId != widget.ideabook.id) {
          r.read(placeholderIdeaProvider.notifier).state = null;
        }
      });
    }
  }

  /// Build loading indicator
  Widget _buildLoadingIndicator(BuildContext context) {
    return const Center(child: CircularProgressIndicator());
  }

  /// Build error indicator
  Widget _buildErrorIndicator(BuildContext context, Object error) {
    // Sanitize the error message
    final sanitizedError = ErrorUtils.sanitizeErrorMessage(error);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: NoejiTheme.colorsOf(context).error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading ideas',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            sanitizedError,
            style: NoejiTheme.textStylesOf(context).bodySmall,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Refresh the ideas using the notifier
              if (_firestoreController != null) {
                _firestoreController!.startListening();
              } else {
                // If controller is not available, create it
                _safelyUseRef((r) {
                  _firestoreController = r.read(
                    firestoreIdeasListenerControllerProvider(
                      widget.ideabook.id,
                    ).notifier,
                  );
                  _firestoreController?.startListening();
                });
              }
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Watch the ideabook for reactive updates (including todo mode changes)
    final currentIdeabookAsync = ref.watch(ideabookStreamProvider(widget.ideabook.id));
    
    // Use the current ideabook from stream, fallback to widget.ideabook if not loaded
    final currentIdeabook = currentIdeabookAsync.maybeWhen(
      data: (ideabook) {
        Logger.debug('IdeasTab: Using updated ideabook from stream - showAsTodoList: ${ideabook?.showAsTodoList}');
        return ideabook ?? widget.ideabook;
      },
      orElse: () {
        Logger.debug('IdeasTab: Using fallback ideabook from widget - showAsTodoList: ${widget.ideabook.showAsTodoList}');
        return widget.ideabook;
      },
    );

    // Get ideas for this ideabook using the combined notifier provider for real-time updates
    final ideasProvider = ref.watch(
      combinedIdeasNotifierProvider(currentIdeabook.id),
    );
    final ideasAsync = ref.watch(ideasProvider);

    // Watch the placeholder idea state
    final placeholderIdea = ref.watch(placeholderIdeaProvider);
    final hasPlaceholder =
        placeholderIdea != null &&
        placeholderIdea.ideabookId == currentIdeabook.id;

    // Watch search state
    final searchQuery = ref.watch(ideasSearchQueryProvider);
    final isSearching = searchQuery.trim().isNotEmpty;
    
    // Watch global search bar visibility preference
    final showSearchBar = ref.watch(searchBarVisibilityProvider);
    
    // Watch hide completed ideas preference
    final hideCompletedIdeas = ref.watch(hideCompletedIdeasProvider);

    return Scaffold(
      body: GestureDetector(
        onTap: () {
          // Unfocus any active text field when tapping outside
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Fixed search bar at top (if enabled)
            if (showSearchBar) const IdeasSearchBar(),
            
            // Main content - expanded to fill remaining space
            Expanded(
              child: Stack(
                children: [
                  // Main scrollable content
                  ideasAsync.when(
                    loading: () => _buildLoadingIndicator(context),
                    error: (error, _) => _buildErrorIndicator(context, error),
                    data: (ideas) {
                      // Apply search filter if searching
                      var displayIdeas = isSearching 
                          ? ref.watch(filteredIdeasProvider(ideas))
                          : ideas;
                      
                      // Apply hide completed ideas filter if enabled and in todo mode
                      if (hideCompletedIdeas && currentIdeabook.showAsTodoList) {
                        displayIdeas = displayIdeas.where((idea) => idea.isDone != true).toList();
                      }

                      // Build the content without search bar (moved to top)
                      return _buildIdeasContent(
                        context, 
                        displayIdeas, 
                        isSearching, 
                        searchQuery, 
                        hasPlaceholder,
                        placeholderIdea,
                        currentIdeabook,
                      );
                    },
                  ),

                  // Fixed floating buttons
                  _FixedFloatingButtons(ideabook: currentIdeabook),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the ideas content based on the current state
  Widget _buildIdeasContent(
    BuildContext context,
    List<Idea> ideas,
    bool isSearching,
    String searchQuery,
    bool hasPlaceholder,
    PlaceholderIdea? placeholderIdea,
    Ideabook currentIdeabook,
  ) {
    // If searching and no results, show empty search results
    if (isSearching && ideas.isEmpty) {
      return Column(
        children: [
          // Show placeholder idea if it exists, even in search mode
          if (hasPlaceholder && placeholderIdea != null) ...[
            PlaceholderIdeaItem(placeholder: placeholderIdea),
            Divider(
              height: 1,
              thickness: 0.5,
              color: NoejiTheme.colorsOf(context).divider,
            ),
          ],
          Expanded(
            child: EmptySearchResults(searchQuery: searchQuery),
          ),
        ],
      );
    }

    // If not searching, no ideas, and no placeholder, show empty state
    if (!isSearching && ideas.isEmpty && !hasPlaceholder) {
      return _buildEmptyState(context);
    }

    // If we have ideas or placeholder, show the layout properly for reordering
    return Column(
      children: [
        // Show placeholder idea at the top if it exists
        if (hasPlaceholder && placeholderIdea != null) ...[
          PlaceholderIdeaItem(placeholder: placeholderIdea),
          if (ideas.isNotEmpty)
            Divider(
              height: 1,
              thickness: 0.5,
              color: NoejiTheme.colorsOf(context).divider,
            ),
        ],
        
        // Ideas list - expanded to fill remaining space for proper auto-scroll
        if (ideas.isNotEmpty)
          Expanded(
            child: ReorderableIdeasList(
              ideabook: currentIdeabook,
              ideas: ideas,
              shrinkWrap: false, // Allow list to fill available space
              physics: null, // Use default scrollable physics
            ),
          )
        else if (hasPlaceholder && placeholderIdea != null)
          // If we have a placeholder but no ideas, add space
          const Expanded(child: SizedBox()),
      ],
    );
  }

  /// Build empty state when there are no ideas
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: NoejiTheme.colorsOf(context).textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No ideas yet',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the "New Idea" button to add your first idea',
            style: NoejiTheme.textStylesOf(context).bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Fixed floating buttons widget for adding new ideas
class _FixedFloatingButtons extends ConsumerWidget {
  final Ideabook ideabook;

  const _FixedFloatingButtons({required this.ideabook});

  /// Check ideas limit and handle paywall if needed
  /// Returns true if the user can proceed, false if they should not
  Future<bool> _checkIdeasLimitAndHandlePaywall(WidgetRef ref, BuildContext context) async {
    try {
      // Check if the ideabook is full
      final ideaRepository = ref.read(ideaRepositoryProvider);
      final isFull = await ideaRepository.isIdeabookFull(ideabook.id);

      if (isFull) {
        // Create a fake exception to trigger paywall handling
        final limitsHelper = ref.read(repositoryLimitsHelperProvider);
        await limitsHelper.throwIdeasLimitException();
      }

      return true; // No limit reached, can proceed
    } catch (e) {
      if (e is IdeasLimitException) {
        // Handle the limit exception with paywall logic
        if (context.mounted) {
          final shouldContinue = await PaywallHandler.handleLimitException(
            context: context,
            ref: ref,
            exception: e,
          );

          return shouldContinue;
        }
        return false;
      } else {
        // Handle other exceptions
        Logger.error('Unexpected error checking ideas limit', e);
        return false;
      }
    }
  }

  void _onNewIdeaTap(WidgetRef ref, BuildContext context) async {
    // Check limits and handle paywall if needed
    final canProceed = await _checkIdeasLimitAndHandlePaywall(ref, context);
    if (!canProceed) {
      // User cancelled paywall or hit limit - don't start recording
      Logger.debug(
        'User cancelled paywall or hit limit, not starting new idea recording',
      );
      return;
    }

    // Request microphone permission before entering recording mode
    final recordingService = ref.read(audioRecordingServiceProvider);

    // Check if permission is already granted
    final hasPermission = await recordingService.checkPermission();
    if (hasPermission) {
      // Enter recording mode
      ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
          IdeabookDetailBottomPanelState.recording;
      return;
    }

    // Request permission
    final status = await recordingService.requestPermission();
    if (status.isGranted) {
      // Enter recording mode
      ref.read(ideabookDetailBottomPanelStateProvider.notifier).state =
          IdeabookDetailBottomPanelState.recording;
      return;
    }

    // If permission is denied, show settings dialog
    if (context.mounted) {
      // Use a post-frame callback to show the dialog after the current frame is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          showDialog(
            context: context,
            builder:
                (dialogContext) => AlertDialog(
                  title: Text(
                    'Microphone Permission Required',
                    style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
                  ),
                  content: Text(
                    'Microphone permission is required to record audio. '
                    'Please enable it in app settings.',
                    style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                    side: BorderSide(
                      color: NoejiTheme.colorsOf(dialogContext).border,
                      width: 1,
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(dialogContext).pop(),
                      child: Text(
                        'Cancel',
                        style:
                            NoejiTheme.textStylesOf(dialogContext).buttonText,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(dialogContext).pop();
                        openAppSettings();
                      },
                      child: Text(
                        'Open Settings',
                        style:
                            NoejiTheme.textStylesOf(dialogContext).buttonText,
                      ),
                    ),
                  ],
                ),
          );
        }
      });
    }
  }

  void _onTypeNewIdeaTap(WidgetRef ref, BuildContext context) async {
    // Provide haptic feedback for type new idea action
    HapticFeedbackService.trigger(HapticAction.typeNewIdea);

    // Check limits and handle paywall if needed
    if (context.mounted) {
      final canProceed = await _checkIdeasLimitAndHandlePaywall(ref, context);
      if (!canProceed) {
        // User cancelled paywall or hit limit - don't create placeholder
        Logger.debug(
          'User cancelled paywall or hit limit, not creating placeholder idea',
        );
        return;
      }
    }

    // Exit any existing edit mode with auto-save before creating new placeholder
    await EditModeHelper.exitIdeaEditModeWithAutoSave(ref);

    // Create a placeholder idea for this ideabook
    PlaceholderIdeaHelper.createPlaceholder(ref, ideabook.id);
    Logger.debug('Type New Idea tapped for ideabook: ${ideabook.id}');
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = NoejiTheme.colorsOf(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Watch for recording state, placeholder, and edit mode to hide buttons when active
    final panelState = ref.watch(ideabookDetailBottomPanelStateProvider);
    final placeholderIdea = ref.watch(placeholderIdeaProvider);
    final hasPlaceholder =
        placeholderIdea != null &&
        placeholderIdea.ideabookId == ideabook.id;

    // Watch for idea edit mode
    final currentEditIdeaId = ref.watch(ideaEditProvider);
    final ideaEditRecording = ref.watch(ideaEditRecordingProvider);
    final isAnyIdeaInEditMode = currentEditIdeaId != null;
    final isIdeaRecording = ideaEditRecording != null;

    // Hide buttons when recording, placeholder exists, or any idea is being edited
    final shouldHide =
        panelState == IdeabookDetailBottomPanelState.recording ||
        hasPlaceholder ||
        isAnyIdeaInEditMode ||
        isIdeaRecording;

    if (shouldHide) {
      return const SizedBox.shrink(); // Hide the button completely
    }

    return Positioned(
      bottom: 16,
      right: 16,
      child: Container(
        width: 56,
        height: 112, // Height for two sections
        decoration: BoxDecoration(
          color: isDark ? const Color(0xFF2D2D2D) : const Color(0xFFF8F8F8),
          border: Border.all(color: colors.border, width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top section: Pencil icon for typing new idea
            Expanded(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _onTypeNewIdeaTap(ref, context),
                  child: Center(
                    child: Icon(
                      Icons.edit,
                      size: 24,
                      color: colors.textPrimary,
                    ),
                  ),
                ),
              ),
            ),
            
            // Separator line
            Container(
              height: 1,
              color: colors.border,
            ),
            
            // Bottom section: Microphone icon for recording new idea
            Expanded(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // Provide haptic feedback for recording start
                    HapticFeedbackService.trigger(HapticAction.recordingStart);
                    _onNewIdeaTap(ref, context);
                  },
                  child: Center(
                    child: Icon(
                      Icons.mic,
                      size: 24,
                      color: colors.textPrimary,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
