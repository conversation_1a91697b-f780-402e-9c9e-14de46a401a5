import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/providers/ideas_search_provider.dart';
import 'package:noeji/ui/providers/search_bar_visibility_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Custom search bar widget for ideas
class IdeasSearchBar extends ConsumerStatefulWidget {
  /// Constructor
  const IdeasSearchBar({super.key});

  @override
  ConsumerState<IdeasSearchBar> createState() => _IdeasSearchBarState();
}

class _IdeasSearchBarState extends ConsumerState<IdeasSearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    final hasFocus = _focusNode.hasFocus;
    ref.read(isSearchingIdeasProvider.notifier).state = hasFocus;
    // Update local state and trigger rebuild
    setState(() {
      _isFocused = hasFocus;
    });
  }

  void _clearSearch() {
    _controller.clear();
    ref.read(ideasSearchQueryProvider.notifier).state = '';
    _focusNode.unfocus();
  }

  void _handleXButtonPress() {
    final searchQuery = ref.read(ideasSearchQueryProvider);
    
    if (searchQuery.trim().isNotEmpty) {
      // If there's text, clear it and keep focus
      _controller.clear();
      ref.read(ideasSearchQueryProvider.notifier).state = '';
      _focusNode.requestFocus();
    } else {
      // If there's no text, hide the search bar
      ref.read(searchBarVisibilityProvider.notifier).setVisibility(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = NoejiTheme.colorsOf(context);
    
    // Watch for focus trigger
    final focusTrigger = ref.watch(searchBarFocusTriggerProvider);
    
    // Handle focus trigger
    if (focusTrigger) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
        // Reset the trigger
        ref.read(searchBarFocusTriggerProvider.notifier).state = false;
      });
    }

    return Container(
      margin: const EdgeInsets.only(
        top: 16, 
        left: 10, 
        right: 10, 
        bottom: 8,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: _isFocused ? colors.textPrimary : colors.divider,
          width: 1,
        ),
        // No border radius for strict rectangle
      ),
      child: Focus(
        onKeyEvent: (node, event) {
          // Handle Escape key to clear search and unfocus
          if (event is KeyDownEvent &&
              event.logicalKey == LogicalKeyboardKey.escape) {
            _clearSearch();
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        },
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          decoration: InputDecoration(
            hintText: 'Search ideas...',
            prefixIcon: Icon(
              Icons.search,
              color: colors.textSecondary,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                Icons.clear,
                color: colors.textSecondary,
              ),
              onPressed: _handleXButtonPress,
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(vertical: 12),
            hintStyle: NoejiTheme.textStylesOf(context).searchHint,
          ),
          style: NoejiTheme.textStylesOf(context).bodyMedium,
          onChanged: (value) {
            ref.read(ideasSearchQueryProvider.notifier).state = value;
          },
          // Handle Enter key to trigger search and unfocus
          onSubmitted: (_) {
            _focusNode.unfocus();
          },
          textInputAction: TextInputAction.search,
        ),
      ),
    );
  }
}