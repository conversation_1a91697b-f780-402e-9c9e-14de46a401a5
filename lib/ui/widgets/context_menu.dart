import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/services/export_service.dart';
import 'package:noeji/ui/providers/color_filter_provider.dart';
import 'package:noeji/ui/providers/combined_filter_provider.dart';
import 'package:noeji/ui/providers/group_provider.dart';
import 'package:noeji/ui/providers/sort_provider.dart';
import 'package:noeji/ui/screens/custom_paywall_screen.dart';
import 'package:noeji/ui/screens/settings_screen.dart';
import 'package:noeji/ui/screens/sign_in_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/noeji_pro_logo.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/file_export_util.dart';
import 'package:noeji/ui/providers/export_state_provider.dart';

// Import the menu state provider
import 'package:noeji/ui/screens/ideabooks_list_screen.dart'
    show isMenuOpenProvider;

/// Context menu for the app
class ContextMenu extends ConsumerWidget {
  /// Constructor
  const ContextMenu({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sign in option (only show if user is not signed in)
          Consumer(
            builder: (context, ref, child) {
              final isSignedIn = ref.watch(isSignedInProvider);

              if (!isSignedIn) {
                // User is not signed in, show sign in option
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.person,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Text(
                        'Sign in',
                        style: NoejiTheme.textStylesOf(context).bodyMedium,
                      ),
                      onTap: () {
                        // Reset menu state
                        ref.read(isMenuOpenProvider.notifier).state = false;

                        // Close the menu
                        Navigator.of(context).pop();

                        // Navigate to sign in screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SignInScreen(),
                          ),
                        );
                      },
                    ),
                    // Divider after sign in
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                // User is signed in, don't show anything (no profile section)
                return const SizedBox.shrink();
              }
            },
          ),

          // Upgrade to Pro (only show if user is signed in and not already pro)
          Consumer(
            builder: (context, ref, child) {
              final authProcess = ref.watch(authProcessProvider);
              final isSignedIn = ref.watch(isSignedInProvider);
              final isProUser = ref.watch(realtimeIsProUserProvider);

              // If signing out, hide the upgrade option
              if (authProcess == AuthProcess.signingOut) {
                return const SizedBox.shrink();
              }

              if (isSignedIn && !isProUser) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.workspace_premium,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Row(
                        children: [
                          Text(
                            'Upgrade to ',
                            style: NoejiTheme.textStylesOf(context).bodyMedium,
                          ),
                          const NoejiProLogo(height: 20),
                        ],
                      ),
                      onTap: () async {
                        // Reset menu state
                        ref.read(isMenuOpenProvider.notifier).state = false;

                        // Close the menu
                        Navigator.of(context).pop();

                        // Navigate to the custom paywall screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const CustomPaywallScreen(),
                          ),
                        );
                      },
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),

          // Settings (only show if user is signed in)
          Consumer(
            builder: (context, ref, child) {
              final isSignedIn = ref.watch(isSignedInProvider);

              if (isSignedIn) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.settings,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Text(
                        'Settings',
                        style: NoejiTheme.textStylesOf(context).bodyMedium,
                      ),
                      onTap: () {
                        // Reset menu state
                        ref.read(isMenuOpenProvider.notifier).state = false;

                        // Close the menu
                        Navigator.of(context).pop();

                        // Navigate to settings screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SettingsScreen(),
                          ),
                        );
                      },
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),

          // Export Ideabooks (only show if user is signed in)
          Consumer(
            builder: (context, ref, child) {
              final isSignedIn = ref.watch(isSignedInProvider);

              if (isSignedIn) {
                return Column(
                  children: [
                    // Export section header
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'Export Ideabooks',
                          style: NoejiTheme.textStylesOf(context).bodyMedium,
                        ),
                      ),
                    ),

                    // Export as Markdown
                    Consumer(
                      builder: (context, ref, child) {
                        final exportState = ref.watch(exportStateProvider);
                        final isExportingMarkdown = exportState == ExportState.exportingMarkdown;
                        
                        return ListTile(
                          leading: isExportingMarkdown
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : Icon(
                                  Icons.article,
                                  color: Theme.of(context).iconTheme.color,
                                ),
                          title: Text(
                            isExportingMarkdown ? 'Exporting...' : 'Export as Markdown',
                            style: NoejiTheme.textStylesOf(context).bodyMedium,
                          ),
                          enabled: exportState == ExportState.idle,
                          onTap: exportState == ExportState.idle ? () async {
                            // Reset menu state and close the menu
                            ref.read(isMenuOpenProvider.notifier).state = false;
                            Navigator.of(context).pop();

                            // Show temporary notification
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Exporting as Markdown...'),
                                  duration: Duration(minutes: 5), // Long enough for export, but not infinite
                                ),
                              );
                            }

                            // Set exporting state
                            ref.read(exportStateProvider.notifier).state = ExportState.exportingMarkdown;

                            // Export as markdown
                            try {
                              final exportService = ref.read(exportServiceProvider);
                              final markdownContent = await exportService.exportAsMarkdown();
                              
                              // Hide the temporary notification
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              }
                              
                              final success = await FileExportUtil.exportMarkdownFile(markdownContent);

                              if (success) {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Markdown file exported successfully'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                }
                              }
                              // Note: Don't show error for !success as it could be user cancellation
                            } catch (e) {
                              Logger.error('Export as markdown failed: $e');
                              // Hide the temporary notification
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              }
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to export as Markdown'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            } finally {
                              // Always reset export state regardless of context state
                              // This ensures the spinner and UI state are properly cleaned up
                              ref.read(exportStateProvider.notifier).state = ExportState.idle;

                              // Try to dismiss any remaining notifications as a fallback
                              // This handles cases where the context became unmounted during file dialog
                              try {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                }
                              } catch (e) {
                                // Ignore errors when trying to dismiss notifications
                                Logger.debug('Could not dismiss notification in finally block: $e');
                              }
                            }
                          } : null,
                        );
                      },
                    ),

                    // Export as HTML
                    Consumer(
                      builder: (context, ref, child) {
                        final exportState = ref.watch(exportStateProvider);
                        final isExportingHtml = exportState == ExportState.exportingHtml;
                        final isProUser = ref.watch(realtimeIsProUserProvider);
                        
                        if (isProUser) {
                          // Pro user - normal functionality
                          return ListTile(
                            leading: isExportingHtml
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : Icon(
                                    Icons.language,
                                    color: Theme.of(context).iconTheme.color,
                                  ),
                            title: Text(
                              isExportingHtml ? 'Exporting...' : 'Export as HTML',
                              style: NoejiTheme.textStylesOf(context).bodyMedium,
                            ),
                            enabled: exportState == ExportState.idle,
                            onTap: exportState == ExportState.idle ? () async {
                              // Reset menu state and close the menu
                              ref.read(isMenuOpenProvider.notifier).state = false;
                              Navigator.of(context).pop();

                              // Show temporary notification
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Exporting as HTML...'),
                                    duration: Duration(minutes: 5), // Long enough for export, but not infinite
                                  ),
                                );
                              }

                              // Set exporting state
                              ref.read(exportStateProvider.notifier).state = ExportState.exportingHtml;

                              // Export as HTML
                              try {
                                final exportService = ref.read(exportServiceProvider);
                                final htmlContent = await exportService.exportAsHtml();
                                
                                // Hide the temporary notification
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                }
                                
                                final success = await FileExportUtil.exportHtmlFile(htmlContent);
                                
                                if (success) {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('HTML file exported successfully'),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                  }
                                }
                                // Note: Don't show error for !success as it could be user cancellation
                              } catch (e) {
                                Logger.error('Export as HTML failed: $e');
                                // Hide the temporary notification
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                }
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Failed to export as HTML'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              } finally {
                                // Always reset export state regardless of context state
                                // This ensures the spinner and UI state are properly cleaned up
                                ref.read(exportStateProvider.notifier).state = ExportState.idle;

                                // Try to dismiss any remaining notifications as a fallback
                                // This handles cases where the context became unmounted during file dialog
                                try {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                  }
                                } catch (e) {
                                  // Ignore errors when trying to dismiss notifications
                                  Logger.debug('Could not dismiss notification in finally block: $e');
                                }
                              }
                            } : null,
                          );
                        } else {
                          // Free user - entire area clickable to trigger paywall
                          return GestureDetector(
                            onTap: () {
                              // Reset menu state and close the menu
                              ref.read(isMenuOpenProvider.notifier).state = false;
                              Navigator.of(context).pop();
                              
                              // Navigate to paywall
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const CustomPaywallScreen(),
                                ),
                              );
                            },
                            child: Container(
                              color: Colors.transparent,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(top: 2),
                                      child: Icon(
                                        Icons.language,
                                        color: Theme.of(context).disabledColor,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Export as HTML',
                                            style: NoejiTheme.textStylesOf(context).bodyMedium.copyWith(
                                              color: Theme.of(context).disabledColor,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                'Unlock with ',
                                                style: NoejiTheme.textStylesOf(context).bodySmall.copyWith(
                                                  color: Theme.of(context).hintColor,
                                                ),
                                              ),
                                              Transform.translate(
                                                offset: const Offset(0, 1),
                                                child: const NoejiProLogo(height: 14),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    ),


                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),

          // Sort order
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final sortOrder = ref.watch(ideabookSortOrderProvider);
                  final isAscending = sortOrder == SortOrder.ascending;

                  return ListTile(
                    leading: Icon(
                      Icons.sort,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    title: Text(
                      'Sort order',
                      style: NoejiTheme.textStylesOf(context).bodyMedium,
                    ),
                    subtitle: Text(
                      isAscending ? 'Oldest first' : 'Newest first',
                      style: NoejiTheme.textStylesOf(context).bodySmall,
                    ),
                    trailing: Icon(
                      isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    onTap: () {
                      // Toggle sort order using the notifier method
                      Logger.debug(
                        'Toggling sort order from ${sortOrder.name}',
                      );
                      ref
                          .read(ideabookSortOrderProvider.notifier)
                          .toggleSortOrder();

                      // Force a rebuild of the ideabooks list
                      ref.invalidate(sortOrderLoggerProvider);

                      // Force a refresh of the combined filtered ideabooks provider
                      ref.invalidate(combinedFilteredIdeabooksProvider);

                      // Reset menu state
                      ref.read(isMenuOpenProvider.notifier).state = false;

                      // Close the menu
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),

              Divider(
                height: 1,
                thickness: 1,
                color: NoejiTheme.colorsOf(context).divider,
              ),
            ],
          ),

          // Group by color
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final isGroupByColorEnabled = ref.watch(groupByColorProvider);

                  return ListTile(
                    leading: Icon(
                      Icons.color_lens,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    title: Text(
                      'Group by color',
                      style: NoejiTheme.textStylesOf(context).bodyMedium,
                    ),
                    trailing: Icon(
                      isGroupByColorEnabled
                          ? Icons.check_box
                          : Icons.check_box_outline_blank,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    onTap: () {
                      // Toggle group by color using the notifier method
                      Logger.debug(
                        'Toggling group by color from $isGroupByColorEnabled to ${!isGroupByColorEnabled}',
                      );
                      ref
                          .read(groupByColorProvider.notifier)
                          .toggleGroupByColor();

                      // Force a refresh of the combined filtered ideabooks provider
                      Logger.debug(
                        'Invalidating combinedFilteredIdeabooksProvider',
                      );
                      ref.invalidate(combinedFilteredIdeabooksProvider);

                      // Force a rebuild of the group by color logger
                      ref.invalidate(groupByColorLoggerProvider);

                      // Reset menu state
                      ref.read(isMenuOpenProvider.notifier).state = false;

                      // Close the menu
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),

              Divider(
                height: 1,
                thickness: 1,
                color: NoejiTheme.colorsOf(context).divider,
              ),
            ],
          ),

          // Filter by color
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                leading: Icon(
                  Icons.filter_list,
                  color: Theme.of(context).iconTheme.color,
                  size: 20,
                ),
                title: Text(
                  'Filter by color',
                  style: NoejiTheme.textStylesOf(context).bodyMedium,
                ),
              ),

              // Color options in a single row with equal spacing
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Get all colors except 'none'
                    final colors =
                        IdeabookColor.values
                            .where((color) => color != IdeabookColor.none)
                            .toList();
                    final colorCount = colors.length;
                    final colorSize = 24.0;

                    // Calculate available width for spacing with increased spacing between colors
                    final availableWidth = constraints.maxWidth;
                    final totalColorWidth = colorCount * colorSize;
                    final totalSpacingWidth = availableWidth - totalColorWidth;

                    // Increase spacing between colors by reducing edge padding
                    // Use 95% of available space for between-color spacing, 5% for edge padding
                    final betweenColorSpacing =
                        (totalSpacingWidth * 0.95) / (colorCount - 1);
                    final edgeSpacing = (totalSpacingWidth * 0.05) / 2;

                    return Row(
                      children: [
                        // Add initial edge spacing
                        SizedBox(width: edgeSpacing),
                        // Add colors with spacing
                        for (int i = 0; i < colors.length; i++) ...[
                          GestureDetector(
                            onTap: () {
                              // Set color filter
                              ref.read(colorFilterProvider.notifier).state =
                                  colors[i];
                              // Reset menu state
                              ref.read(isMenuOpenProvider.notifier).state =
                                  false;
                              // Close the menu
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              width: colorSize,
                              height: colorSize,
                              decoration: BoxDecoration(
                                color: NoejiTheme.getIdeabookColor(
                                  context,
                                  colors[i].index,
                                ),
                                border: Border.all(
                                  color: NoejiTheme.colorsOf(context).border,
                                  // Remove border by intention. Do not change back to 1.
                                  width: 0,
                                ),
                              ),
                            ),
                          ),
                          // Add spacing after each color except the last one
                          if (i < colors.length - 1)
                            SizedBox(width: betweenColorSpacing),
                        ],
                        // Add final edge spacing
                        SizedBox(width: edgeSpacing),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
