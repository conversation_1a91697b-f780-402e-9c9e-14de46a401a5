import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/providers/auth_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/ui/widgets/thank_you_dialog.dart';

/// Screen for displaying user profile information
class UserProfileScreen extends ConsumerWidget {
  /// Constructor
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the current user
    final user = ref.watch(currentUserProvider);

    // If user is null, show error
    if (user == null) {
      return Scaffold(
        appBar: AppBar(
          title: null,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Center(
          child: Text(
            'User not signed in',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: null,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Profile picture
            if (user.photoUrl != null)
              CircleAvatar(
                radius: 50,
                backgroundImage: NetworkImage(user.photoUrl!),
              )
            else
              CircleAvatar(
                radius: 50,
                backgroundColor: NoejiTheme.colorsOf(context).border,
                child: Icon(
                  Icons.person,
                  size: 50,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),

            const SizedBox(height: 24),

            // User name
            Text(
              user.displayName,
              style: GoogleFonts.afacad(
                fontSize: 24,
                fontWeight: FontWeight.w500,
                color: NoejiTheme.colorsOf(context).textPrimary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // User email
            Text(
              user.email,
              style: GoogleFonts.afacad(
                fontSize: 16,
                color: NoejiTheme.colorsOf(context).textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // App User ID section
            _buildAppUserIdSection(context, ref),

            const SizedBox(height: 40),

            // Subscription button
            _buildSubscriptionButton(context, ref),

            const SizedBox(height: 40),

            // Sign out button
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border.all(
                  color: NoejiTheme.colorsOf(context).border,
                  width: 1, // Changed from 2pt to 1pt as requested
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () async {
                    try {
                      // Show loading dialog
                      _showLoadingDialog(context);

                      // Get the safe sign out function
                      final signOut = ref.read(signOutProvider);

                      // Sign out with listener cleanup
                      await signOut();

                      // Close loading dialog
                      if (context.mounted) {
                        Navigator.of(context).pop(); // Close dialog
                        // Navigation will be handled automatically by the global navigation logic
                        // when authProcessProvider changes from signingOut back to idle
                      }
                    } catch (e) {
                      // Close loading dialog
                      if (context.mounted) {
                        Navigator.of(context).pop();
                      }

                      // Show error message
                      Logger.error('Error signing out', e);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Error signing out: ${e.toString()}',
                              style: GoogleFonts.afacad(),
                            ),
                          ),
                        );
                      }
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.logout,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Sign out',
                          style: GoogleFonts.afacad(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: NoejiTheme.colorsOf(context).textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the subscription button to show the RevenueCat paywall
  Widget _buildSubscriptionButton(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () async {
          try {
            // Show the RevenueCat paywall
            final result = await RevenueCatUI.presentPaywall();

            // Handle the result
            switch (result) {
              case PaywallResult.purchased:
                if (context.mounted) {
                  // Show thank you dialog first
                  await ThankYouDialog.showThankYouDialog(context);

                  // Check context again after async operation
                  if (context.mounted) {
                    // Then show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Thank you for your purchase!',
                          style: GoogleFonts.afacad(),
                        ),
                      ),
                    );
                  }
                }
                break;
              case PaywallResult.restored:
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Your purchase has been restored!',
                        style: GoogleFonts.afacad(),
                      ),
                    ),
                  );
                }
                break;
              case PaywallResult.cancelled:
                // User cancelled the purchase, no action needed
                break;
              case PaywallResult.error:
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'There was an error processing your purchase.',
                        style: GoogleFonts.afacad(),
                      ),
                    ),
                  );
                }
                break;
              case PaywallResult.notPresented:
                Logger.error('Paywall was not presented');
                break;
            }
          } catch (e) {
            Logger.error('Error showing paywall', e);
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Error showing subscription options: ${e.toString()}',
                    style: GoogleFonts.afacad(),
                  ),
                ),
              );
            }
          }
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.workspace_premium,
                color: NoejiTheme.colorsOf(context).textPrimary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'Upgrade to Premium',
                style: GoogleFonts.afacad(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the app user ID section with copy button
  Widget _buildAppUserIdSection(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'App User ID',
            style: GoogleFonts.afacad(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            'If you need to contact support about your subscription, please provide this ID:',
            style: GoogleFonts.afacad(
              fontSize: 14,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),

          const SizedBox(height: 12),

          // App User ID with copy button
          Consumer(
            builder: (context, ref, child) {
              // Watch the app user ID provider
              final appUserIdAsync = ref.watch(appUserIdProvider);

              return appUserIdAsync.when(
                data: (appUserId) {
                  if (appUserId == null) {
                    return Text(
                      'Not available',
                      style: GoogleFonts.afacad(
                        fontSize: 14,
                        color: NoejiTheme.colorsOf(context).textSecondary,
                      ),
                    );
                  }

                  return Row(
                    children: [
                      // App User ID
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color:
                                NoejiTheme.colorsOf(
                                  context,
                                ).chatMessageBackground,
                            border: Border.all(
                              color: NoejiTheme.colorsOf(context).border,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            appUserId,
                            style: GoogleFonts.afacad(
                              fontSize: 14,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                      // Copy button
                      IconButton(
                        icon: const Icon(Icons.copy),
                        onPressed: () {
                          // Copy to clipboard
                          Clipboard.setData(ClipboardData(text: appUserId));

                          // Show snackbar
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'App User ID copied to clipboard',
                                style: GoogleFonts.afacad(),
                              ),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                      ),
                    ],
                  );
                },
                loading:
                    () => const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                error: (error, stackTrace) {
                  Logger.error('Error loading app user ID', error);
                  return Text(
                    'Error loading ID: ${error.toString()}',
                    style: GoogleFonts.afacad(fontSize: 14, color: Colors.red),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  /// Show a loading dialog
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Dialog(
            backgroundColor: Theme.of(context).cardColor,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(width: 20),
                  Text(
                    'Signing out...',
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
