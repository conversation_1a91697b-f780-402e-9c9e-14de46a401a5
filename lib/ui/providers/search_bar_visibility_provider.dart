import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/preferences/app_behavior_storage.dart';
import 'package:noeji/utils/logger.dart';

/// State notifier for search bar visibility preference
class SearchBarVisibilityNotifier extends StateNotifier<bool> {
  /// Constructor - starts with default false value (search bar hidden by default)
  SearchBarVisibilityNotifier() : super(false) {
    _loadSavedPreference();
  }

  /// Load the saved preference from storage
  Future<void> _loadSavedPreference() async {
    try {
      final showSearchBar = await AppBehaviorStorage.loadShowSearchBar();
      state = showSearchBar;
      Logger.debug('Search bar visibility preference loaded: $showSearchBar');
    } catch (e) {
      Logger.error('Error loading search bar visibility preference', e);
      // Keep default value (false) on error
    }
  }

  /// Toggle the search bar visibility
  Future<void> toggle() async {
    try {
      final newValue = !state;
      final success = await AppBehaviorStorage.saveShowSearchBar(newValue);
      
      if (success) {
        state = newValue;
        Logger.debug('Search bar visibility toggled to: $newValue');
      } else {
        Logger.error('Failed to save search bar visibility preference');
      }
    } catch (e) {
      Logger.error('Error toggling search bar visibility', e);
    }
  }

  /// Set the search bar visibility
  Future<void> setVisibility(bool visible) async {
    try {
      final success = await AppBehaviorStorage.saveShowSearchBar(visible);
      
      if (success) {
        state = visible;
        Logger.debug('Search bar visibility set to: $visible');
      } else {
        Logger.error('Failed to save search bar visibility preference');
      }
    } catch (e) {
      Logger.error('Error setting search bar visibility', e);
    }
  }
}

/// Provider for search bar visibility preference
final searchBarVisibilityProvider = StateNotifierProvider<SearchBarVisibilityNotifier, bool>((ref) {
  return SearchBarVisibilityNotifier();
});