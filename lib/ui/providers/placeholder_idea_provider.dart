import 'package:flutter_riverpod/flutter_riverpod.dart';

/// State for a placeholder idea that hasn't been saved yet
class PlaceholderIdea {
  /// The ideabook ID this placeholder belongs to
  final String ideabookId;

  /// The current content being typed
  final String content;

  /// Whether the placeholder is currently in edit mode
  final bool isEditing;

  /// Creates a new PlaceholderIdea
  const PlaceholderIdea({
    required this.ideabookId,
    required this.content,
    this.isEditing = true,
  });

  /// Creates a copy with updated values
  PlaceholderIdea copyWith({
    String? ideabookId,
    String? content,
    bool? isEditing,
  }) {
    return PlaceholderIdea(
      ideabookId: ideabookId ?? this.ideabookId,
      content: content ?? this.content,
      isEditing: isEditing ?? this.isEditing,
    );
  }
}

/// Provider to track the current placeholder idea state
/// null means no placeholder idea exists
final placeholderIdeaProvider = StateProvider<PlaceholderIdea?>((ref) => null);

/// Helper functions for managing placeholder ideas
class PlaceholderIdeaHelper {
  /// Create a new placeholder idea for the given ideabook
  static void createPlaceholder(WidgetRef ref, String ideabookId) {
    ref.read(placeholderIdeaProvider.notifier).state = PlaceholderIdea(
      ideabookId: ideabookId,
      content: '',
      isEditing: true,
    );
  }

  /// Update the content of the current placeholder idea
  static void updateContent(WidgetRef ref, String content) {
    final current = ref.read(placeholderIdeaProvider);
    if (current != null) {
      ref.read(placeholderIdeaProvider.notifier).state = current.copyWith(
        content: content,
      );
    }
  }

  /// Cancel the current placeholder idea
  static void cancelPlaceholder(WidgetRef ref) {
    ref.read(placeholderIdeaProvider.notifier).state = null;
  }

  /// Check if there's a placeholder for the given ideabook
  static bool hasPlaceholder(WidgetRef ref, String ideabookId) {
    final placeholder = ref.read(placeholderIdeaProvider);
    return placeholder != null && placeholder.ideabookId == ideabookId;
  }

  /// Get the placeholder for the given ideabook (if any)
  static PlaceholderIdea? getPlaceholder(WidgetRef ref, String ideabookId) {
    final placeholder = ref.read(placeholderIdeaProvider);
    if (placeholder != null && placeholder.ideabookId == ideabookId) {
      return placeholder;
    }
    return null;
  }
}
