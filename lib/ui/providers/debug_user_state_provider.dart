import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/utils/logger.dart';

/// Debug provider to force specific user states for testing
/// This should only be used in debug builds for testing purposes
class DebugUserStateNotifier extends StateNotifier<UserState?> {
  DebugUserStateNotifier() : super(null);

  /// Set a specific user state for testing
  void setUserState(UserState userState) {
    state = userState;
    Logger.debug('Debug user state set to: $userState');
  }

  /// Clear the debug user state (use real state)
  void clearOverride() {
    state = null;
    Logger.debug('Debug user state override cleared - using real state');
  }

  /// Reset to default (no override)
  void reset() {
    state = null;
    Logger.debug('Debug user state reset to: null (no override)');
  }

  /// Check if an override is active
  bool get isOverrideActive => state != null;

  /// Get a user-friendly description of the current debug state
  String get debugDescription {
    if (state == null) {
      return 'No override (using real user state)';
    }
    switch (state!) {
      case UserState.newUser:
        return 'New User (needs to start trial or subscribe)';
      case UserState.trialUser:
        return 'Trial User (full access during trial)';
      case UserState.proUser:
        return 'Pro User (full paid access)';
      case UserState.freeUser:
        return 'Free User (read-only access)';
      case UserState.unknown:
        return 'Unknown User State';
    }
  }
}

/// Provider for debug user state override
/// When set to a specific state, this will force the app to use that user state
/// regardless of the actual subscription status
final debugUserStateProvider =
    StateNotifierProvider<DebugUserStateNotifier, UserState?>((ref) {
      return DebugUserStateNotifier();
    });
