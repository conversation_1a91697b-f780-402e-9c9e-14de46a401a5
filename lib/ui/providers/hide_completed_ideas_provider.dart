import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/preferences/app_behavior_storage.dart';
import 'package:noeji/utils/logger.dart';

/// State notifier for hide completed ideas preference
class HideCompletedIdeasNotifier extends StateNotifier<bool> {
  /// Constructor - starts with default false value (show completed ideas by default)
  HideCompletedIdeasNotifier() : super(false) {
    _loadSavedPreference();
  }

  /// Load the saved preference from storage
  Future<void> _loadSavedPreference() async {
    try {
      final hideCompletedIdeas = await AppBehaviorStorage.loadHideCompletedIdeas();
      state = hideCompletedIdeas;
      Logger.debug('Hide completed ideas preference loaded: $hideCompletedIdeas');
    } catch (e) {
      Logger.error('Error loading hide completed ideas preference', e);
      // Keep default value (false) on error
    }
  }

  /// Toggle the hide completed ideas preference
  Future<void> toggle() async {
    try {
      final newValue = !state;
      final success = await AppBehaviorStorage.saveHideCompletedIdeas(newValue);
      
      if (success) {
        state = newValue;
        Logger.debug('Hide completed ideas preference toggled to: $newValue');
      } else {
        Logger.error('Failed to save hide completed ideas preference');
      }
    } catch (e) {
      Logger.error('Error toggling hide completed ideas preference', e);
    }
  }

  /// Set the hide completed ideas preference
  Future<void> setVisibility(bool hide) async {
    try {
      final success = await AppBehaviorStorage.saveHideCompletedIdeas(hide);
      
      if (success) {
        state = hide;
        Logger.debug('Hide completed ideas preference set to: $hide');
      } else {
        Logger.error('Failed to save hide completed ideas preference');
      }
    } catch (e) {
      Logger.error('Error setting hide completed ideas preference', e);
    }
  }
}

/// Provider for hide completed ideas preference
final hideCompletedIdeasProvider = StateNotifierProvider<HideCompletedIdeasNotifier, bool>((ref) {
  return HideCompletedIdeasNotifier();
});