import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/common/filter_provider.dart';

/// Provider for the ideas search query
final ideasSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider for the ideas search filter
final ideasSearchFilterProvider = Provider<FilterProvider<Idea>>((ref) {
  final query = ref.watch(ideasSearchQueryProvider);
  final filter = FilterProvider<Idea>();

  if (query.trim().isNotEmpty) {
    // Add filter for searching idea content
    filter.addCriterion(
      _IdeaSearchFilterCriterion(query: query.toLowerCase()),
    );
  }

  return filter;
});

/// Provider for filtered ideas based on search query
final filteredIdeasProvider =
    Provider.family<List<Idea>, List<Idea>>((ref, ideas) {
      final filter = ref.watch(ideasSearchFilterProvider);
      return filter.apply(ideas);
    });

/// Provider for ideas search state
final isSearchingIdeasProvider = StateProvider<bool>((ref) => false);

/// Provider to trigger focus on search bar
final searchBarFocusTriggerProvider = StateProvider<bool>((ref) => false);

/// Custom filter criterion for ideas that searches both content and date
class _IdeaSearchFilterCriterion implements FilterCriteria<Idea> {
  final String query;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  final DateFormat _shortDateFormat = DateFormat('MM-dd');
  final DateFormat _monthYearFormat = DateFormat('yyyy-MM');

  _IdeaSearchFilterCriterion({required this.query});

  @override
  bool get isActive => query.trim().isNotEmpty;

  @override
  bool apply(Idea item) {
    final lowerQuery = query.toLowerCase();
    
    // Search in content
    if (item.content.toLowerCase().contains(lowerQuery)) {
      return true;
    }
    
    // Search in date (multiple formats)
    final createdAt = item.createdAt;
    
    // Full date format (yyyy-MM-dd)
    if (_dateFormat.format(createdAt).contains(lowerQuery)) {
      return true;
    }
    
    // Short date format (MM-dd)
    if (_shortDateFormat.format(createdAt).contains(lowerQuery)) {
      return true;
    }
    
    // Month-year format (yyyy-MM)
    if (_monthYearFormat.format(createdAt).contains(lowerQuery)) {
      return true;
    }
    
    // Individual date components
    final year = createdAt.year.toString();
    final month = createdAt.month.toString().padLeft(2, '0');
    final day = createdAt.day.toString().padLeft(2, '0');
    
    if (year.contains(lowerQuery) || 
        month.contains(lowerQuery) || 
        day.contains(lowerQuery)) {
      return true;
    }
    
    return false;
  }
}