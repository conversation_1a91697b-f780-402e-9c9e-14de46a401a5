import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/preferences/sort_order_storage.dart';
import 'package:noeji/utils/logger.dart';

/// Enum for sort order
enum SortOrder {
  /// Ascending order (oldest first)
  ascending,

  /// Descending order (newest first)
  descending,
}

/// Notifier for managing sort order state with persistence
class SortOrderNotifier extends StateNotifier<SortOrder> {
  /// Constructor
  SortOrderNotifier() : super(SortOrder.descending) {
    // Load saved sort order when initialized
    _loadSavedSortOrder();
  }

  /// Toggle between ascending and descending sort order
  void toggleSortOrder() {
    final newOrder =
        state == SortOrder.ascending
            ? SortOrder.descending
            : SortOrder.ascending;
    state = newOrder;
    // Save the new sort order preference
    SortOrderStorage.saveSortOrder(newOrder);
  }

  /// Set sort order explicitly
  void setSortOrder(SortOrder order) {
    state = order;
    // Save the new sort order preference
    SortOrderStorage.saveSortOrder(order);
  }

  /// Load the saved sort order from storage
  Future<void> _loadSavedSortOrder() async {
    final savedOrder = await SortOrderStorage.loadSortOrder();
    state = savedOrder;
  }
}

/// Provider for the ideabook sort order with persistence
final ideabookSortOrderProvider =
    StateNotifierProvider<SortOrderNotifier, SortOrder>((ref) {
      return SortOrderNotifier();
    });

/// Notifier to log sort order changes
final sortOrderLoggerProvider = Provider<void>((ref) {
  ref.listen(ideabookSortOrderProvider, (previous, next) {
    Logger.debug('Sort order changed from ${previous?.name} to ${next.name}');
  });
});
