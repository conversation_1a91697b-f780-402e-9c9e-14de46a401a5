import 'package:flutter_riverpod/flutter_riverpod.dart';

/// State for tracking idea edit information for auto-save functionality
class IdeaEditState {
  final String? currentEditIdeaId;
  final String? currentEditContent;
  final String? currentIdeabookId;

  const IdeaEditState({
    this.currentEditIdeaId,
    this.currentEditContent,
    this.currentIdeabookId,
  });

  IdeaEditState copyWith({
    String? currentEditIdeaId,
    String? currentEditContent,
    String? currentIdeabookId,
  }) {
    return IdeaEditState(
      currentEditIdeaId: currentEditIdeaId ?? this.currentEditIdeaId,
      currentEditContent: currentEditContent ?? this.currentEditContent,
      currentIdeabookId: currentIdeabookId ?? this.currentIdeabookId,
    );
  }
}

/// Provider to track the current edit state for auto-save functionality
final ideaEditStateProvider = StateProvider<IdeaEditState>(
  (ref) => const IdeaEditState(),
);
