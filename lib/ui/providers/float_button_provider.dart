import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/preferences/float_button_storage.dart';
import 'package:noeji/utils/logger.dart';

/// State notifier for floating button expanded state
class FloatButtonNotifier extends StateNotifier<bool> {
  /// Constructor - starts with collapsed state (false)
  FloatButtonNotifier() : super(false) {
    // Load saved state when initialized
    _loadSavedState();
  }

  /// Load the saved expanded state from storage
  Future<void> _loadSavedState() async {
    try {
      final savedState = await FloatButtonStorage.loadIsExpanded(
        defaultValue: false, // Default to collapsed
      );
      
      // Update state only if it's different to avoid unnecessary rebuilds
      if (state != savedState) {
        state = savedState;
        Logger.debug('Float button expanded state loaded: $savedState');
      }
    } catch (e) {
      Logger.error('Error loading float button expanded state', e);
      // Keep default state (false) on error
    }
  }

  /// Set the expanded state and save to persistent storage
  Future<void> setExpanded(bool isExpanded) async {
    try {
      // Update state immediately for responsive UI
      state = isExpanded;
      
      // Save to persistent storage
      final success = await FloatButtonStorage.saveIsExpanded(isExpanded);
      
      if (success) {
        Logger.debug('Float button expanded state updated: $isExpanded');
      } else {
        Logger.error('Failed to save float button expanded state');
        // State is already updated for UI responsiveness, 
        // but log the storage failure
      }
    } catch (e) {
      Logger.error('Error setting float button expanded state', e);
      // State is already updated for UI responsiveness
    }
  }

  /// Toggle the expanded state
  Future<void> toggle() async {
    await setExpanded(!state);
  }

  /// Reset to default state (collapsed)
  Future<void> reset() async {
    try {
      await FloatButtonStorage.resetIsExpanded();
      state = false; // Reset to default collapsed state
      Logger.debug('Float button state reset to default');
    } catch (e) {
      Logger.error('Error resetting float button state', e);
      // Still update state for UI consistency
      state = false;
    }
  }
}

/// Provider for floating button expanded state
final floatButtonProvider = StateNotifierProvider<FloatButtonNotifier, bool>((ref) {
  return FloatButtonNotifier();
});

/// Convenience provider for just reading the expanded state
final floatButtonExpandedProvider = Provider<bool>((ref) {
  return ref.watch(floatButtonProvider);
});