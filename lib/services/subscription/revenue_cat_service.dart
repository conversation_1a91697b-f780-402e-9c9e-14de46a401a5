import 'dart:io';
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/services/subscription/user_tier_cache.dart';
import 'package:noeji/utils/logger.dart';

/// Service for managing subscriptions with RevenueCat
class RevenueCatService {
  /// Current user ID
  String? _currentUserId;

  /// Flag to track if RevenueCat has been configured for a specific user
  bool _isConfigured = false;

  /// Flag to track if CustomerInfo listener has been set up
  bool _isCustomerInfoListenerSetup = false;

  /// Stream controller for CustomerInfo updates
  final _customerInfoController = StreamController<CustomerInfo>.broadcast();

  /// API keys for different platforms
  static const String _apiKeyApple = 'appl_SoqrOluRqYxyqwytgBmpHTBiKHi';
  static const String _apiKeyAndroid = 'goog_zQlTXgGjamlweXDNtWxaXxmbGqu';

  /// Check if RevenueCat is configured for a specific user
  /// This should be checked before calling any RevenueCat methods
  bool get isConfigured => _isConfigured;

  /// Stream of CustomerInfo updates
  /// This provides a centralized way to listen to customer info changes
  Stream<CustomerInfo> get customerInfoStream => _customerInfoController.stream;

  /// Initialize RevenueCat with the current user
  /// This should be called after the user is authenticated
  /// Returns immediately if already configured for same user
  Future<void> initialize({required String userId}) async {
    // Skip initialization on unsupported platforms
    if (!(Platform.isIOS || Platform.isAndroid)) {
      Logger.debug(
        'Skipping RevenueCat initialization on unsupported platform',
      );
      return;
    }

    try {
      // Case 1: Already configured AND for the SAME user
      if (_isConfigured && _currentUserId == userId) {
        Logger.debug('RevenueCat already initialized with user ID: $userId');
        // Optionally, ensure listener is setup if it somehow wasn't
        _setupCustomerInfoListener();
        return;
      }

      // Case 2: Already configured BUT for a DIFFERENT user
      if (_isConfigured && _currentUserId != null && _currentUserId != userId) {
        Logger.debug(
          'User ID changed from $_currentUserId to $userId. Logging out old RC user and logging in new RC user.',
        );
        await Purchases.logOut(); // Log out the old RC user first
        // No need to set _isConfigured = false here, as we are immediately logging in.
      }

      // If we reach here, we need to either configure for the first time
      // or logIn a new user after a previous one was logged out

      if (!_isConfigured || _currentUserId == null) {
        // Indicates first time config or after a full logout
        Logger.debug('Configuring RevenueCat for user ID: $userId');
        final apiKey = Platform.isIOS ? _apiKeyApple : _apiKeyAndroid;
        await Purchases.configure(
          PurchasesConfiguration(apiKey)..appUserID = userId,
        );
        if (kDebugMode) {
          await Purchases.setLogLevel(LogLevel.debug);
        }
        _isConfigured = true; // Now it's configured
      } else {
        // Was configured for a different user, we logged them out, now log in new user
        Logger.debug('Logging in to RevenueCat with new user ID: $userId');
        await Purchases.logIn(userId);
        // _isConfigured should still be true or re-asserted if needed
        _isConfigured = true;
      }

      _currentUserId = userId;
      _setupCustomerInfoListener();

      Logger.debug(
        'RevenueCat initialized/logged in successfully for user: $userId',
      );
    } catch (e) {
      Logger.error(
        'Failed to initialize/login to RevenueCat for user $userId',
        e,
      );
      rethrow;
    }
  }

  /// Initialize RevenueCat in background (non-blocking)
  /// This allows immediate app startup while RevenueCat initializes
  void initializeInBackground({required String userId}) {
    // Run initialization in background without blocking caller
    initialize(userId: userId).catchError((error) {
      Logger.error(
        'Background RevenueCat initialization failed for user $userId',
        error,
      );
    });
  }

  /// Logout from RevenueCat
  /// This should be called when the app user signs out
  Future<void> logout() async {
    // Store the current user ID for cache cleanup
    final userIdToCleanup = _currentUserId;
    
    // Only attempt to log out if RevenueCat was actually configured for a user.
    if (!_isConfigured) {
      Logger.debug(
        'RevenueCatService: SDK not configured or already logged out. Skipping RevenueCat logout.',
      );
      // Still clean up cache if we have a user ID
      if (userIdToCleanup != null) {
        await _cleanupUserCache(userIdToCleanup);
      }
      return;
    }

    try {
      Logger.debug('Logging out from RevenueCat for user ID: $_currentUserId');
      await Purchases.logOut();
      Logger.debug('RevenueCat logout successful.');
    } catch (e) {
      Logger.error('Failed to logout from RevenueCat', e);
      // Decide if you want to rethrow or just log. Usually, app logout should proceed.
    } finally {
      // CRITICAL: Reset your service's state after logout.
      _currentUserId = null;
      _isConfigured =
          false; // Mark as not configured for any specific user now.
      _isCustomerInfoListenerSetup = false; // Reset listener setup flag
      
      // Clean up user cache
      if (userIdToCleanup != null) {
        await _cleanupUserCache(userIdToCleanup);
      }
    }
  }

  /// Clean up user tier cache for the specified user
  Future<void> _cleanupUserCache(String userId) async {
    try {
      await UserTierCache.instance.clearCache(userId);
      Logger.debug('RevenueCatService: Cleaned up cache for user: $userId');
    } catch (e) {
      Logger.error('RevenueCatService: Failed to clean up cache for user: $userId', e);
    }
  }

  /// Set up CustomerInfo listener for real-time updates
  /// This is called automatically during initialization
  void _setupCustomerInfoListener() {
    // Only set up the listener once per app session
    if (_isCustomerInfoListenerSetup) {
      Logger.debug('CustomerInfo listener already set up, skipping');
      return;
    }

    // Check if RevenueCat is configured before setting up listener
    if (!_isConfigured) {
      Logger.debug(
        'RevenueCat not configured yet, skipping CustomerInfo listener setup',
      );
      return;
    }

    try {
      // Set up the CustomerInfo update listener
      // This will trigger whenever CustomerInfo changes from purchases, restores, etc.
      Purchases.addCustomerInfoUpdateListener((customerInfo) {
        Logger.debug('RevenueCatService: Received CustomerInfo update');
        Logger.debug(
          'RevenueCatService: Active entitlements: ${customerInfo.entitlements.active.keys.join(', ')}',
        );
        Logger.debug(
          'RevenueCatService: Broadcasting CustomerInfo to ${_customerInfoController.hasListener ? 'active' : 'no'} listeners',
        );

        // Broadcast the customer info to all listeners
        if (!_customerInfoController.isClosed) {
          _customerInfoController.add(customerInfo);
          Logger.debug(
            'RevenueCatService: CustomerInfo broadcasted successfully',
          );
        } else {
          Logger.error(
            'RevenueCatService: CustomerInfo controller is closed, cannot broadcast',
          );
        }
      });

      _isCustomerInfoListenerSetup = true;
      Logger.debug(
        'RevenueCatService: CustomerInfo listener set up successfully',
      );
    } catch (e) {
      Logger.error(
        'RevenueCatService: Failed to set up CustomerInfo listener',
        e,
      );
    }
  }

  /// Get the current customer info
  Future<CustomerInfo?> getCustomerInfo() async {
    if (!_isConfigured) {
      Logger.debug(
        'RevenueCatService: Purchases SDK not configured yet. Skipping getCustomerInfo().',
      );
      return null;
    }

    try {
      final customerInfo = await Purchases.getCustomerInfo();
      Logger.debug(
        'Retrieved customer info: ${customerInfo.originalAppUserId}',
      );
      return customerInfo;
    } catch (e) {
      Logger.error('Failed to get customer info', e);
      rethrow;
    }
  }

  /// Get the current offerings
  Future<Offerings?> getOfferings() async {
    if (!_isConfigured) {
      Logger.debug(
        'RevenueCatService: Purchases SDK not configured yet. Skipping getOfferings().',
      );
      return null;
    }

    try {
      final offerings = await Purchases.getOfferings();
      Logger.debug('Retrieved offerings: ${offerings.all.length} available');
      return offerings;
    } catch (e) {
      Logger.error('Failed to get offerings', e);
      rethrow;
    }
  }

  /// Get the current app user ID
  Future<String?> getAppUserId() async {
    if (!_isConfigured) {
      Logger.debug(
        'RevenueCatService: Purchases SDK not configured yet. Skipping getAppUserId().',
      );
      return null;
    }

    try {
      final customerInfo = await Purchases.getCustomerInfo();
      return customerInfo.originalAppUserId;
    } catch (e) {
      Logger.error('Failed to get app user ID', e);
      rethrow;
    }
  }

  /// Force refresh CustomerInfo from RevenueCat servers
  /// This is useful after purchases or when the app resumes from background
  Future<CustomerInfo?> refreshCustomerInfo() async {
    if (!_isConfigured) {
      Logger.debug(
        'RevenueCatService: Purchases SDK not configured yet. Skipping refreshCustomerInfo().',
      );
      return null;
    }

    try {
      Logger.debug('RevenueCatService: Force refreshing CustomerInfo');
      // getCustomerInfo() will fetch fresh data from servers if cache is stale
      final customerInfo = await Purchases.getCustomerInfo();
      Logger.debug('RevenueCatService: CustomerInfo refreshed successfully');

      // Broadcast the refreshed customer info to all listeners
      if (!_customerInfoController.isClosed) {
        _customerInfoController.add(customerInfo);
      }

      return customerInfo;
    } catch (e) {
      Logger.error('RevenueCatService: Failed to refresh CustomerInfo', e);
      rethrow;
    }
  }

  /// Dispose the service and clean up resources
  void dispose() {
    Logger.debug('RevenueCatService: Disposing service');
    _customerInfoController.close();
  }
}
