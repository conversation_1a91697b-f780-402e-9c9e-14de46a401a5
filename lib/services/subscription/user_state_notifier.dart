import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/subscription/user_state_service.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/services/subscription/user_tier_cache.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Real-time user state that updates automatically when CustomerInfo changes
class UserStateNotifier extends StateNotifier<AsyncValue<UserState>> {
  final Ref _ref;
  final String? _userId;
  UserState? _previousState;
  bool _isDisposed = false;
  StreamSubscription<CustomerInfo>? _customerInfoSubscription;

  // Cache for the user's trial eligibility.
  Map<String, IntroEligibility> _eligibilityMap = {};

  UserStateNotifier(this._ref, [this._userId]) : super(const AsyncValue.loading()) {
    _initialize();
  }

  /// Constructor for non-signed-in users that immediately sets unknown state
  UserStateNotifier.forUnauthenticatedUser(this._ref)
    : _userId = null,
      super(const AsyncValue.data(UserState.unknown)) {
    // Don't initialize RevenueCat listeners for unauthenticated users
    Logger.debug('UserStateNotifier: Created for unauthenticated user');
    _previousState = UserState.unknown;
  }

  /// Initialize the notifier by setting up listeners and fetching initial state
  Future<void> _initialize() async {
    if (_isDisposed) return;

    Logger.debug('UserStateNotifier: Initializing');

    try {
      // Load cached state first if available (non-blocking)
      if (_userId != null) {
        final cachedState = await UserTierCache.instance.getCachedUserState(_userId);
        if (cachedState != null) {
          Logger.debug('UserStateNotifier: Using cached user state: $cachedState');
          state = AsyncValue.data(cachedState);
          _previousState = cachedState;
        }
      }

      // Get the RevenueCat service
      final revenueCatService = _ref.read(revenueCatServiceProvider);

      // Wait for RevenueCat to be configured
      // This ensures we don't try to listen before the service is ready
      while (!revenueCatService.isConfigured && !_isDisposed) {
        Logger.debug(
          'UserStateNotifier: Waiting for RevenueCat to be configured...',
        );
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (_isDisposed) return;

      Logger.debug(
        'UserStateNotifier: RevenueCat is configured, setting up listeners',
      );

      // Listen to the centralized CustomerInfo stream from RevenueCatService
      _customerInfoSubscription = revenueCatService.customerInfoStream.listen(
        (customerInfo) {
          // Check if this notifier has been disposed to avoid "Bad state" errors
          if (_isDisposed) {
            Logger.debug(
              'UserStateNotifier: Ignoring CustomerInfo update - notifier is disposed',
            );
            return;
          }

          Logger.debug(
            'UserStateNotifier: Received CustomerInfo update from stream',
          );
          _updateUserState(customerInfo); // Use the synchronous update method
        },
        onError: (error) {
          if (_isDisposed) return;
          Logger.error(
            'UserStateNotifier: Error in CustomerInfo stream',
            error,
          );
          // Default to unknown state on error to be safe
          if (!_isDisposed) {
            state = const AsyncValue.data(UserState.unknown);
            _previousState = UserState.unknown;
          }
        },
      );

      // Get initial customer info and eligibility (non-blocking background fetch)
      _fetchInitialStateAndEligibilityInBackground();
    } catch (e) {
      if (_isDisposed) return;
      Logger.error('UserStateNotifier: Error during initialization', e);
      if (!_isDisposed) {
        state = AsyncValue.error(e, StackTrace.current);
      }
    }
  }

  /// Non-blocking background fetch that doesn't block initialization
  void _fetchInitialStateAndEligibilityInBackground() {
    // Run in background without awaiting to avoid blocking
    _fetchInitialStateAndEligibility().catchError((error) {
      Logger.error('UserStateNotifier: Background fetch failed', error);
    });
  }

  // This method now fetches all necessary data.
  Future<void> _fetchInitialStateAndEligibility() async {
    if (_isDisposed) return;
    Logger.debug(
      'UserStateNotifier: Fetching initial state and trial eligibility',
    );

    try {
      final revenueCatService = _ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug(
          'UserStateNotifier: RevenueCat not configured yet, defaulting to unknown state',
        );
        if (!_isDisposed) {
          state = const AsyncValue.data(UserState.unknown);
          _previousState = UserState.unknown;
        }
        return;
      }

      // Fetch all required data in parallel for efficiency
      final results = await Future.wait([
        revenueCatService.getCustomerInfo(),
        Purchases.getOfferings(),
      ]);

      final customerInfo = results[0] as CustomerInfo?;
      final offerings = results[1] as Offerings;

      if (customerInfo == null) {
        if (!_isDisposed) state = const AsyncValue.data(UserState.unknown);
        return;
      }

      // Check eligibility for products in the current offering
      final currentOffering = offerings.current;
      if (currentOffering != null &&
          currentOffering.availablePackages.isNotEmpty) {
        final productIds =
            currentOffering.availablePackages
                .map((p) => p.storeProduct.identifier)
                .toList();
        _eligibilityMap =
            await Purchases.checkTrialOrIntroductoryPriceEligibility(
              productIds,
            );
        Logger.debug(
          'UserStateNotifier: Fetched eligibility: $_eligibilityMap',
        );
      } else {
        _eligibilityMap = {}; // No offerings, no eligibility to check
      }

      // Update state with all the new data
      _updateUserState(customerInfo);
    } catch (e) {
      if (_isDisposed) return;
      Logger.error(
        'UserStateNotifier: Error fetching initial state/eligibility',
        e,
      );
      if (!_isDisposed) state = AsyncValue.error(e, StackTrace.current);
    }
  }

  // This is now a synchronous update method.
  void _updateUserState(CustomerInfo customerInfo) {
    if (_isDisposed) return;

    Logger.debug(
      'UserStateNotifier: Updating user state using CustomerInfo and cached eligibility',
    );

    try {
      final userStateService = UserStateService.instance;
      // Pass the cached eligibility map to the service
      final newUserState = userStateService.getUserState(
        customerInfo,
        _eligibilityMap,
      );

      Logger.debug(
        'UserStateNotifier: User state determined as: $newUserState',
      );

      // Check if user state changed and handle pro settings reset
      final isInitialLoad = _previousState == null;
      final stateChanged =
          _previousState != null && _previousState != newUserState;
      final becameFreeUser = stateChanged && newUserState == UserState.freeUser;
      final isFreeUserOnInitialLoad =
          isInitialLoad && newUserState == UserState.freeUser;

      // Only update if the state has actually changed
      if (_previousState != newUserState) {
        Logger.debug(
          'UserStateNotifier: User state changed from $_previousState to $newUserState',
        );

        if (!_isDisposed) {
          state = AsyncValue.data(newUserState);
        }

        // Cache the new state for faster startup next time
        if (_userId != null) {
          UserTierCache.instance.cacheUserState(_userId, newUserState);
        }

        // Reset pro settings only for free users (subscription expired)
        // Do NOT reset for new users (never had subscription)
        if (becameFreeUser) {
          Logger.debug(
            'UserStateNotifier: User became free user (subscription expired), resetting pro settings',
          );
          _resetProSettingsForFreeUser();
        } else if (isFreeUserOnInitialLoad) {
          // Also reset on initial load if user is free (handles app startup for users whose subscription has expired)
          Logger.debug(
            'UserStateNotifier: Initial load detected free user (subscription expired), resetting pro settings',
          );
          _resetProSettingsForFreeUser();
        }

        _previousState = newUserState;
      } else {
        Logger.debug('UserStateNotifier: User state unchanged: $newUserState');
      }
    } catch (e) {
      if (_isDisposed) return;
      Logger.error('UserStateNotifier: Error updating user state', e);
      if (!_isDisposed) {
        state = AsyncValue.error(e, StackTrace.current);
      }
    }
  }

  /// Reset pro settings for free users only
  /// This method is called when a user's subscription expires (becomes freeUser)
  /// It is NOT called for new users who never had a subscription
  void _resetProSettingsForFreeUser() {
    // Check if this notifier has been disposed
    if (_isDisposed) {
      Logger.debug(
        'UserStateNotifier: Ignoring pro settings reset - notifier is disposed',
      );
      return;
    }

    try {
      // Use the ref to access the app behavior provider and reset settings
      final appBehaviorNotifier = _ref.read(appBehaviorProvider.notifier);
      // Reset all pro settings to defaults for free users (subscription expired)
      appBehaviorNotifier.resetToDefaults();
      Logger.debug(
        'UserStateNotifier: Pro settings reset for free user (subscription expired)',
      );
    } catch (e) {
      Logger.error(
        'UserStateNotifier: Failed to reset pro settings for free user',
        e,
      );
    }
  }

  /// Force refresh now re-fetches eligibility as well.
  /// This is crucial to call after a purchase or restore.
  Future<void> refresh() async {
    if (_isDisposed) return;

    Logger.debug(
      'UserStateNotifier: Force refreshing user state and eligibility',
    );
    await _fetchInitialStateAndEligibility();
  }

  @override
  void dispose() {
    Logger.debug('UserStateNotifier: Disposing');
    _isDisposed = true;
    _customerInfoSubscription?.cancel();
    super.dispose();
  }
}
