import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/utils/logger.dart';

/// Local cache for user tier status to improve app startup performance
/// and provide offline access to subscription status
class UserTierCache {
  static const String _keyUserState = 'user_tier_cache_state';
  static const String _keyUserTier = 'user_tier_cache_tier';
  static const String _keyLastUpdated = 'user_tier_cache_last_updated';
  static const String _keyUserId = 'user_tier_cache_user_id';
  
  static UserTierCache? _instance;
  static UserTierCache get instance => _instance ??= UserTierCache._();
  
  UserTierCache._();

  /// Cache user state (4-state model) for a specific user
  Future<void> cacheUserState(String userId, UserState userState) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyUserState, userState.toString());
      await prefs.setString(_keyUserId, userId);
      await prefs.setInt(_keyLastUpdated, DateTime.now().millisecondsSinceEpoch);
      
      Logger.debug('UserTierCache: Cached user state: $userState for user: $userId');
    } catch (e) {
      Logger.error('UserTierCache: Failed to cache user state', e);
    }
  }

  /// Cache user tier (legacy 2-state model) for a specific user
  Future<void> cacheUserTier(String userId, String userTier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyUserTier, userTier);
      await prefs.setString(_keyUserId, userId);
      await prefs.setInt(_keyLastUpdated, DateTime.now().millisecondsSinceEpoch);
      
      Logger.debug('UserTierCache: Cached user tier: $userTier for user: $userId');
    } catch (e) {
      Logger.error('UserTierCache: Failed to cache user tier', e);
    }
  }

  /// Get cached user state for a specific user
  /// Returns null if no cache exists or cache is for a different user
  Future<UserState?> getCachedUserState(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedUserId = prefs.getString(_keyUserId);
      
      // Check if cache is for the current user
      if (cachedUserId != userId) {
        Logger.debug('UserTierCache: Cache is for different user, returning null');
        return null;
      }
      
      final stateString = prefs.getString(_keyUserState);
      if (stateString == null) {
        Logger.debug('UserTierCache: No cached user state found');
        return null;
      }
      
      final userState = UserState.values.firstWhere(
        (state) => state.toString() == stateString,
        orElse: () => UserState.unknown,
      );
      
      final lastUpdated = prefs.getInt(_keyLastUpdated);
      final cacheAge = lastUpdated != null 
          ? DateTime.now().millisecondsSinceEpoch - lastUpdated
          : 0;
      
      Logger.debug(
        'UserTierCache: Retrieved cached user state: $userState (age: ${cacheAge}ms)',
      );
      return userState;
    } catch (e) {
      Logger.error('UserTierCache: Failed to get cached user state', e);
      return null;
    }
  }

  /// Get cached user tier for a specific user
  /// Returns null if no cache exists or cache is for a different user
  Future<String?> getCachedUserTier(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedUserId = prefs.getString(_keyUserId);
      
      // Check if cache is for the current user
      if (cachedUserId != userId) {
        Logger.debug('UserTierCache: Cache is for different user, returning null');
        return null;
      }
      
      final userTier = prefs.getString(_keyUserTier);
      
      if (userTier == null) {
        Logger.debug('UserTierCache: No cached user tier found');
        return null;
      }
      
      final lastUpdated = prefs.getInt(_keyLastUpdated);
      final cacheAge = lastUpdated != null 
          ? DateTime.now().millisecondsSinceEpoch - lastUpdated
          : 0;
      
      Logger.debug(
        'UserTierCache: Retrieved cached user tier: $userTier (age: ${cacheAge}ms)',
      );
      return userTier;
    } catch (e) {
      Logger.error('UserTierCache: Failed to get cached user tier', e);
      return null;
    }
  }

  /// Clear cache for a specific user or all users
  Future<void> clearCache([String? userId]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (userId != null) {
        // Only clear if cache is for the specified user
        final cachedUserId = prefs.getString(_keyUserId);
        if (cachedUserId == userId) {
          await prefs.remove(_keyUserState);
          await prefs.remove(_keyUserTier);
          await prefs.remove(_keyLastUpdated);
          await prefs.remove(_keyUserId);
          Logger.debug('UserTierCache: Cleared cache for user: $userId');
        }
      } else {
        // Clear all cache
        await prefs.remove(_keyUserState);
        await prefs.remove(_keyUserTier);
        await prefs.remove(_keyLastUpdated);
        await prefs.remove(_keyUserId);
        Logger.debug('UserTierCache: Cleared all cache');
      }
    } catch (e) {
      Logger.error('UserTierCache: Failed to clear cache', e);
    }
  }

  /// Get cache age in milliseconds
  Future<int?> getCacheAge() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdated = prefs.getInt(_keyLastUpdated);
      
      if (lastUpdated == null) {
        return null;
      }
      
      return DateTime.now().millisecondsSinceEpoch - lastUpdated;
    } catch (e) {
      Logger.error('UserTierCache: Failed to get cache age', e);
      return null;
    }
  }

  /// Check if cache exists and is valid for a specific user
  Future<bool> hasValidCache(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedUserId = prefs.getString(_keyUserId);
      
      // Check if cache is for the current user
      if (cachedUserId != userId) {
        return false;
      }
      
      final hasState = prefs.getString(_keyUserState) != null;
      final hasTier = prefs.getString(_keyUserTier) != null;
      final hasTimestamp = prefs.getInt(_keyLastUpdated) != null;
      
      return (hasState || hasTier) && hasTimestamp;
    } catch (e) {
      Logger.error('UserTierCache: Failed to check cache validity', e);
      return false;
    }
  }

  /// Get cached user state with fallback to default
  Future<UserState> getCachedUserStateWithFallback(
    String userId, 
    UserState fallback,
  ) async {
    final cachedState = await getCachedUserState(userId);
    return cachedState ?? fallback;
  }

  /// Get cached user tier with fallback to default
  Future<String> getCachedUserTierWithFallback(
    String userId, 
    String fallback,
  ) async {
    final cachedTier = await getCachedUserTier(userId);
    return cachedTier ?? fallback;
  }
}