import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/subscription/revenue_cat_service.dart';
import 'package:noeji/services/subscription/user_tier_service.dart';
import 'package:noeji/services/subscription/user_tier_notifier.dart';
import 'package:noeji/services/subscription/user_state_service.dart';
import 'package:noeji/services/subscription/user_state_notifier.dart';
import 'package:noeji/ui/providers/debug_user_state_provider.dart';
import 'package:noeji/utils/logger.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

/// Compatibility notifier that maintains the UserTierNotifier interface
/// but delegates to the new 4-state UserStateNotifier system
/// This allows existing code to continue working while using the new system internally
class _UserTierCompatibilityNotifier extends UserTierNotifier {
  final AsyncValue<String> _tierString;

  _UserTierCompatibilityNotifier(super.ref, this._tierString)
    : super.forFreeUser() {
    // Override the state with the converted tier string
    state = _tierString;
  }

  @override
  bool get isProUser {
    return state.value == 'pro';
  }

  @override
  Future<void> refresh() async {
    // This is handled by the underlying UserStateNotifier
    Logger.debug(
      '_UserTierCompatibilityNotifier: Refresh called - delegating to 4-state system',
    );
  }
}

/// Provider for the RevenueCat service
final revenueCatServiceProvider = Provider<RevenueCatService>((ref) {
  return RevenueCatService();
});

/// Provider for the user tier service
final userTierServiceProvider = Provider<UserTierService>((ref) {
  return UserTierService.instance;
});

/// Provider for the real-time user tier notifier
/// MIGRATED: Now delegates to the new 4-state UserStateNotifier system
/// This maintains backward compatibility while using the new 4-state logic internally
final userTierNotifierProvider = StateNotifierProvider<
  UserTierNotifier,
  AsyncValue<String>
>((ref) {
  // This is now a compatibility layer that delegates to the new 4-state system
  // Watch the new 4-state system
  final userStateAsync = ref.watch(userStateNotifierProvider);

  // Convert 4-state to legacy 2-state tier string
  final tierString = userStateAsync.when(
    loading: () => const AsyncValue<String>.loading(),
    error: (error, stackTrace) => AsyncValue<String>.error(error, stackTrace),
    data: (userState) {
      final userStateService = ref.read(userStateServiceProvider);
      final tier = userStateService.getUserTierString(userState);
      return AsyncValue.data(tier);
    },
  );

  // Create a compatibility wrapper that behaves like UserTierNotifier
  // but internally uses the new 4-state system
  return _UserTierCompatibilityNotifier(ref, tierString);
});

/// Provider for real-time user tier as string
/// MIGRATED: Now delegates to the new 4-state UserStateNotifier system
/// This replaces the old userTierProvider with real-time updates
/// In debug mode, this can be overridden by debug providers
final realtimeUserTierProvider = Provider<AsyncValue<String>>((ref) {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      final userStateService = ref.watch(userStateServiceProvider);
      final tierString = userStateService.getUserTierString(debugUserState);
      Logger.debug(
        'Debug user state override is active - tier: $tierString for state: $debugUserState',
      );
      return AsyncValue.data(tierString);
    }
  }

  // MIGRATED: Use the new 4-state system instead of legacy UserTierNotifier
  final userStateAsync = ref.watch(userStateNotifierProvider);

  return userStateAsync.when(
    loading: () => const AsyncValue<String>.loading(),
    error: (error, stackTrace) => AsyncValue<String>.error(error, stackTrace),
    data: (userState) {
      final userStateService = ref.read(userStateServiceProvider);
      final tierString = userStateService.getUserTierString(userState);
      Logger.debug(
        'realtimeUserTierProvider: Converted state $userState to tier: $tierString',
      );
      return AsyncValue.data(tierString);
    },
  );
});

/// Provider for real-time pro user status
/// MIGRATED: Now delegates to the new 4-state UserStateNotifier system
/// This replaces the old isProUserProvider with real-time updates
/// In debug mode, this can be overridden by debug providers
final realtimeIsProUserProvider = Provider<bool>((ref) {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      final userStateService = ref.watch(userStateServiceProvider);
      final hasFullAccess = userStateService.hasFullAccess(debugUserState);
      Logger.debug(
        'Debug user state override is active - hasFullAccess: $hasFullAccess for state: $debugUserState',
      );
      return hasFullAccess;
    }
  }

  // MIGRATED: Use the new 4-state system instead of legacy UserTierNotifier
  final userStateAsync = ref.watch(userStateNotifierProvider);
  final userState = userStateAsync.value;

  if (userState == null) {
    Logger.debug(
      'realtimeIsProUserProvider: User state is null, returning false',
    );
    return false;
  }

  final userStateService = ref.read(userStateServiceProvider);
  final hasFullAccess = userStateService.hasFullAccess(userState);

  Logger.debug(
    'realtimeIsProUserProvider: User state $userState has full access: $hasFullAccess',
  );

  return hasFullAccess;
});

/// Provider for refreshing user tier after purchases
/// MIGRATED: Now delegates to the new 4-state UserStateNotifier system
/// This can be called to force refresh the user tier state
final refreshUserTierProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    try {
      Logger.debug(
        'Refreshing user tier after purchase (delegating to 4-state system)',
      );

      // Check if user is signed in
      final userAsync = ref.read(firebaseUserProvider);
      final user = userAsync.value;
      if (user == null) {
        Logger.debug('User not signed in, skipping user tier refresh');
        return;
      }

      // Check if RevenueCat is configured
      final revenueCatService = ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug('RevenueCat not configured, skipping user tier refresh');
        return;
      }

      // Force refresh CustomerInfo from RevenueCat
      // This will trigger the customerInfoStream that UserStateNotifier listens to
      await revenueCatService.refreshCustomerInfo();

      // MIGRATED: Refresh the new 4-state UserStateNotifier instead of legacy UserTierNotifier
      try {
        final userStateNotifier = ref.read(userStateNotifierProvider.notifier);
        await userStateNotifier.refresh();
        Logger.debug('UserStateNotifier refreshed directly (4-state system)');
      } catch (e) {
        Logger.error('Failed to refresh UserStateNotifier directly', e);
        // Don't fail the overall refresh if this fails
      }

      Logger.debug('User tier refresh completed (via 4-state system)');
    } catch (e) {
      Logger.error('Failed to refresh user tier', e);
      rethrow;
    }
  };
});

/// Provider for checking if the user is a pro user
/// Note: This uses UserTierService which internally calls RevenueCatService methods
/// We ensure RevenueCat is initialized before calling UserTierService methods
/// In debug mode, this can be overridden by debug providers
final isProUserProvider = FutureProvider<bool>((ref) async {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      final userStateService = ref.watch(userStateServiceProvider);
      final hasFullAccess = userStateService.hasFullAccess(debugUserState);
      Logger.debug(
        'Debug user state override is active - hasFullAccess: $hasFullAccess for state: $debugUserState',
      );
      return hasFullAccess;
    }
  }

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, they are not a pro user
  if (user == null) {
    return false;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  try {
    final userTierService = ref.watch(userTierServiceProvider);
    return await userTierService.isProUser();
  } catch (e) {
    Logger.error('Failed to check if user is pro', e);
    return false;
  }
});

/// Provider for getting the user tier as a string
/// Note: This uses UserTierService which internally calls RevenueCatService methods
/// We ensure RevenueCat is initialized before calling UserTierService methods
/// In debug mode, this can be overridden by debug providers
final userTierProvider = FutureProvider<String>((ref) async {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      final userStateService = ref.watch(userStateServiceProvider);
      final tierString = userStateService.getUserTierString(debugUserState);
      Logger.debug(
        'Debug user state override is active - tier: $tierString for state: $debugUserState',
      );
      return tierString;
    }
  }

  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, they are a free user
  if (user == null) {
    return 'free';
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  try {
    final userTierService = ref.watch(userTierServiceProvider);
    return await userTierService.getUserTier();
  } catch (e) {
    Logger.error('Failed to get user tier', e);
    return 'free';
  }
});

/// Provider to ensure RevenueCat is initialized when the user is signed in
/// and logged out when the user signs out
/// Returns a Future that completes when RevenueCat is properly configured for the current user
/// This provider will re-run when firebaseUserProvider emits new values
final ensureRevenueCatInitializedProvider = FutureProvider<void>((ref) async {
  // Watch the current Firebase user
  final userAsync = ref.watch(firebaseUserProvider);
  final revenueCatService = ref.watch(revenueCatServiceProvider);

  Logger.debug(
    'ensureRevenueCatInitializedProvider: Starting initialization check',
  );

  // Handle the AsyncValue properly
  // Note: Treating loading as null allows the provider to complete while Firebase auth is loading
  // The provider will re-run when firebaseUserProvider resolves to a definite state
  final user = userAsync.when(
    loading: () {
      Logger.debug(
        'ensureRevenueCatInitializedProvider: Firebase user loading, treating as no user',
      );
      return null; // Treat loading as no user for now
    },
    error: (error, stackTrace) {
      Logger.error(
        'Error getting user in ensureRevenueCatInitializedProvider',
        error,
      );
      return null;
    },
    data: (user) {
      Logger.debug(
        'ensureRevenueCatInitializedProvider: Firebase user data received: ${user?.uid ?? 'null'}',
      );
      return user;
    },
  );

  if (user != null) {
    // User is signed in, ensure RevenueCat is initialized
    Logger.debug(
      'ensureRevenueCatInitializedProvider: User is signed in, ensuring RevenueCat initialization with user ID: ${user.uid}',
    );
    try {
      // RevenueCatService.initialize() has internal checks to prevent redundant calls
      // for the same user, so this is safe even if called multiple times
      await revenueCatService.initialize(userId: user.uid);
      Logger.debug(
        'ensureRevenueCatInitializedProvider: RevenueCat initialization completed for user: ${user.uid}',
      );
    } catch (e) {
      Logger.error(
        'ensureRevenueCatInitializedProvider: Failed to initialize RevenueCat for user ${user.uid}',
        e,
      );
      rethrow;
    }
  } else {
    // User signed out (or was never signed in, or is loading)
    if (revenueCatService.isConfigured) {
      // Only logout if it was configured for a previous user
      Logger.debug(
        'ensureRevenueCatInitializedProvider: User signed out, ensuring RevenueCat logout',
      );
      try {
        // RevenueCatService.logout() has internal checks to prevent redundant calls
        await revenueCatService.logout();
        Logger.debug(
          'ensureRevenueCatInitializedProvider: RevenueCat logout completed',
        );
      } catch (e) {
        Logger.error(
          'ensureRevenueCatInitializedProvider: Failed to logout from RevenueCat',
          e,
        );
        rethrow;
      }
    } else {
      Logger.debug(
        'ensureRevenueCatInitializedProvider: User signed out, but RevenueCat was not configured',
      );
    }
  }

  Logger.debug(
    'ensureRevenueCatInitializedProvider: Initialization check completed',
  );
});

/// Non-blocking provider that starts RevenueCat initialization in background
/// This allows immediate app startup while RevenueCat initializes
final backgroundRevenueCatInitProvider = Provider<void>((ref) {
  // Get the current Firebase user
  final userAsync = ref.watch(firebaseUserProvider);
  final revenueCatService = ref.read(revenueCatServiceProvider);

  // Handle the AsyncValue properly
  final user = userAsync.when(
    loading: () => firebase_auth.FirebaseAuth.instance.currentUser,
    error: (_, __) => null,
    data: (firebaseUser) => firebaseUser,
  );

  if (user != null) {
    // User is signed in, start background initialization
    Logger.debug(
      'backgroundRevenueCatInitProvider: Starting background RevenueCat initialization for user: ${user.uid}',
    );
    revenueCatService.initializeInBackground(userId: user.uid);
  } else {
    // User is not signed in, logout in background
    Logger.debug(
      'backgroundRevenueCatInitProvider: No user signed in, starting background logout from RevenueCat',
    );
    if (revenueCatService.isConfigured) {
      revenueCatService.logout().catchError((error) {
        Logger.error(
          'backgroundRevenueCatInitProvider: Background logout failed',
          error,
        );
      });
    }
  }
});

/// Provider for customer info from RevenueCat
final customerInfoProvider = FutureProvider<CustomerInfo?>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  // Check if RevenueCat is configured
  final revenueCatService = ref.watch(revenueCatServiceProvider);
  if (!revenueCatService.isConfigured) {
    Logger.debug('RevenueCat not configured, returning null for customer info');
    return null;
  }

  try {
    return await revenueCatService.getCustomerInfo();
  } catch (e) {
    Logger.error('Failed to get customer info', e);
    return null;
  }
});

/// Provider for offerings from RevenueCat
final offeringsProvider = FutureProvider<Offerings?>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  // Check if RevenueCat is configured
  final revenueCatService = ref.watch(revenueCatServiceProvider);
  if (!revenueCatService.isConfigured) {
    Logger.debug('RevenueCat not configured, returning null for offerings');
    return null;
  }

  try {
    return await revenueCatService.getOfferings();
  } catch (e) {
    Logger.error('Failed to get offerings', e);
    return null;
  }
});

/// Provider for the app user ID from RevenueCat
final appUserIdProvider = FutureProvider<String?>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  // Check if RevenueCat is configured
  final revenueCatService = ref.watch(revenueCatServiceProvider);
  if (!revenueCatService.isConfigured) {
    Logger.debug('RevenueCat not configured, returning null for app user ID');
    return null;
  }

  try {
    return await revenueCatService.getAppUserId();
  } catch (e) {
    Logger.error('Failed to get app user ID', e);
    return null;
  }
});

// ============================================================================
// NEW 4-STATE USER MONETIZATION PROVIDERS
// ============================================================================

/// Provider for the UserStateService
final userStateServiceProvider = Provider<UserStateService>((ref) {
  return UserStateService.instance;
});

/// Provider for the real-time user state notifier
/// This provides real-time updates when user state changes based on the 4-state model
/// The notifier lifecycle is tied to Firebase authentication state
final userStateNotifierProvider = StateNotifierProvider<
  UserStateNotifier,
  AsyncValue<UserState>
>((ref) {
  // Watch Firebase user to react to authentication changes
  final firebaseUserAsync = ref.watch(firebaseUserProvider);

  // Get the current user state
  final user = firebaseUserAsync.when(
    loading: () {
      // While loading, check if we can determine auth state synchronously
      final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
      Logger.debug(
        'userStateNotifierProvider: Firebase loading, current user: ${currentUser?.uid ?? 'null'}',
      );
      return currentUser;
    },
    error: (error, stackTrace) {
      // On error, default to no user
      Logger.error('Error in userStateNotifierProvider', error);
      return null;
    },
    data: (firebaseUser) {
      Logger.debug(
        'userStateNotifierProvider: Firebase user data: ${firebaseUser?.uid ?? 'null'}',
      );
      return firebaseUser;
    },
  );

  if (user == null) {
    // No Firebase user, create unauthenticated user notifier
    Logger.debug(
      'userStateNotifierProvider: No user, creating unauthenticated user notifier',
    );
    return UserStateNotifier.forUnauthenticatedUser(ref);
  } else {
    // User is signed in, start background RevenueCat initialization
    // but create notifier immediately with cached data
    ref.read(backgroundRevenueCatInitProvider);
    
    Logger.debug(
      'userStateNotifierProvider: User signed in, creating UserStateNotifier for user: ${user.uid}',
    );
    return UserStateNotifier(ref, user.uid);
  }
});

/// Provider for real-time user state
/// This replaces the old tier-based providers with the new 4-state model
/// In debug mode, this can be overridden by debug providers
final realtimeUserStateProvider = Provider<AsyncValue<UserState>>((ref) {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      Logger.debug(
        'Debug user state override is active - returning: $debugUserState',
      );
      return AsyncValue.data(debugUserState);
    }
  }

  return ref.watch(userStateNotifierProvider);
});

/// Provider for refreshing user state after purchases
/// This can be called to force refresh the user state
final refreshUserStateProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    try {
      Logger.debug('Refreshing user state after purchase');

      // Check if user is signed in
      final userAsync = ref.read(firebaseUserProvider);
      final user = userAsync.value;
      if (user == null) {
        Logger.debug('User not signed in, skipping user state refresh');
        return;
      }

      // Check if RevenueCat is configured
      final revenueCatService = ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug('RevenueCat not configured, skipping user state refresh');
        return;
      }

      // Force refresh CustomerInfo from RevenueCat
      // This will trigger the customerInfoStream that UserStateNotifier listens to
      await revenueCatService.refreshCustomerInfo();

      // Also directly refresh the UserStateNotifier to ensure immediate UI update
      try {
        final userStateNotifier = ref.read(userStateNotifierProvider.notifier);
        await userStateNotifier.refresh();
        Logger.debug('UserStateNotifier refreshed directly');
      } catch (e) {
        Logger.error('Failed to refresh UserStateNotifier directly', e);
        // Don't fail the overall refresh if this fails
      }

      Logger.debug('User state refresh completed');
    } catch (e) {
      Logger.error('Failed to refresh user state', e);
      rethrow;
    }
  };
});

// ============================================================================
// BACKWARD COMPATIBILITY PROVIDERS
// These providers maintain compatibility with existing code that uses the old tier system
// ============================================================================

/// Backward compatibility provider for checking if user has full access (pro or trial)
/// This replaces isProUserProvider with the new 4-state logic
final isProUserCompatProvider = Provider<bool>((ref) {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      final userStateService = ref.watch(userStateServiceProvider);
      final hasFullAccess = userStateService.hasFullAccess(debugUserState);
      Logger.debug(
        'Debug user state override is active - hasFullAccess: $hasFullAccess for state: $debugUserState',
      );
      return hasFullAccess;
    }
  }

  final userStateAsync = ref.watch(userStateNotifierProvider);
  final userState = userStateAsync.value;

  if (userState == null) return false;

  final userStateService = ref.watch(userStateServiceProvider);
  return userStateService.hasFullAccess(userState);
});

/// Backward compatibility provider for getting user tier as string
/// This replaces userTierProvider with the new 4-state logic
final userTierCompatProvider = Provider<String>((ref) {
  // Check if debug user state override is enabled (only in debug builds)
  if (kDebugMode) {
    final debugUserState = ref.watch(debugUserStateProvider);
    if (debugUserState != null) {
      final userStateService = ref.watch(userStateServiceProvider);
      final tierString = userStateService.getUserTierString(debugUserState);
      Logger.debug(
        'Debug user state override is active - tier: $tierString for state: $debugUserState',
      );
      return tierString;
    }
  }

  final userStateAsync = ref.watch(userStateNotifierProvider);
  final userState = userStateAsync.value;

  if (userState == null) return 'free';

  final userStateService = ref.watch(userStateServiceProvider);
  return userStateService.getUserTierString(userState);
});
