import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Service that listens to app lifecycle changes and refreshes user state when app resumes
/// MIGRATED: Now works with the new 4-state UserStateNotifier system
/// This ensures user state transitions are properly handled when the app resumes
/// (e.g., if user becomes paid, they should be reflected as pro; if trial ends, they become free user)
class AppLifecycleService extends WidgetsBindingObserver {
  final Ref _ref;
  bool _isInitialized = false;

  AppLifecycleService(this._ref);

  /// Initialize the lifecycle listener
  void initialize() {
    if (_isInitialized) {
      Logger.debug('AppLifecycleService: Already initialized, skipping');
      return;
    }

    try {
      WidgetsBinding.instance.addObserver(this);
      _isInitialized = true;
      Logger.debug('AppLifecycleService: Initialized successfully');
    } catch (e) {
      Logger.error('AppLifecycleService: Failed to initialize', e);
    }
  }

  /// Dispose the lifecycle listener
  void dispose() {
    if (_isInitialized) {
      try {
        WidgetsBinding.instance.removeObserver(this);
        _isInitialized = false;
        Logger.debug('AppLifecycleService: Disposed successfully');
      } catch (e) {
        Logger.error('AppLifecycleService: Failed to dispose', e);
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    Logger.debug('AppLifecycleService: App lifecycle state changed to: $state');

    // Refresh user state when app resumes from background
    if (state == AppLifecycleState.resumed) {
      _refreshUserStateOnResume();
    }
  }

  /// Refresh user state when app resumes from background
  /// MIGRATED: Now refreshes the 4-state UserStateNotifier system
  /// This handles proper state transitions like:
  /// - User becomes paid -> should be reflected as pro user
  /// - User's free trial ends -> should become free user
  /// - User's subscription expires -> should become free user
  Future<void> _refreshUserStateOnResume() async {
    try {
      Logger.debug(
        'AppLifecycleService: App resumed, considering user state refresh',
      );

      // Check if user is signed in
      final userAsync = _ref.read(firebaseUserProvider);
      final user = userAsync.value;

      // Check if RevenueCat is configured
      final revenueCatService = _ref.read(revenueCatServiceProvider);

      if (user != null && revenueCatService.isConfigured) {
        // User signed in AND RC is configured for them
        Logger.debug(
          'AppLifecycleService: App resumed, user signed in. Refreshing 4-state user state.',
        );

        // Refresh CustomerInfo which will trigger the stream that UserStateNotifier listens to
        // This ensures proper state transitions are handled automatically
        await revenueCatService.refreshCustomerInfo();

        // Also directly refresh the UserStateNotifier to ensure immediate state update
        try {
          final userStateNotifier = _ref.read(
            userStateNotifierProvider.notifier,
          );
          await userStateNotifier.refresh();
          Logger.debug(
            'AppLifecycleService: UserStateNotifier refreshed directly',
          );
        } catch (e) {
          Logger.error(
            'AppLifecycleService: Failed to refresh UserStateNotifier directly',
            e,
          );
          // Don't throw error to avoid disrupting app lifecycle
        }

        Logger.debug(
          'AppLifecycleService: User state refreshed on app resume (4-state system)',
        );
      } else {
        if (user == null) {
          Logger.debug(
            'AppLifecycleService: App resumed, but user not signed in. Skipping state refresh.',
          );
        }
        if (!revenueCatService.isConfigured) {
          Logger.debug(
            'AppLifecycleService: App resumed, but RevenueCat not configured. Skipping state refresh.',
          );
        }
      }
    } catch (e) {
      Logger.error(
        'AppLifecycleService: Failed to refresh user state on app resume',
        e,
      );
      // Don't throw error to avoid disrupting app lifecycle
    }
  }
}

/// Provider for the app lifecycle service
final appLifecycleServiceProvider = Provider<AppLifecycleService>((ref) {
  final service = AppLifecycleService(ref);

  // Initialize the service when it's first accessed
  service.initialize();

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider to ensure app lifecycle service is initialized
/// This should be watched early in the app lifecycle
final ensureAppLifecycleServiceProvider = Provider<void>((ref) {
  // Just reading the service provider will initialize it
  ref.watch(appLifecycleServiceProvider);
  Logger.debug('AppLifecycleService: Ensured initialization');
});
