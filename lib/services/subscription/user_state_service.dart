import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/utils/logger.dart';

/// Service for determining the 4-state user monetization model
/// Based on RevenueCat CustomerInfo data
class UserStateService {
  static UserStateService? _instance;
  static UserStateService get instance => _instance ??= UserStateService._();

  UserStateService._();

  // Note: Using generic approach since specific product/entitlement IDs are not defined
  // The app currently checks for any active entitlements rather than specific ones

  /// Determine user state from CustomerInfo AND pre-fetched eligibility.
  /// This is the core logic that implements the 4-state monetization model.
  UserState getUserState(
    CustomerInfo customerInfo,
    Map<String, IntroEligibility> eligibility,
  ) {
    try {
      Logger.debug('UserStateService: Analyzing CustomerInfo and eligibility');

      final hasActiveEntitlements = customerInfo.entitlements.active.isNotEmpty;

      // 1. Check for active PRO or TRIAL status
      if (hasActiveEntitlements) {
        bool isInTrial = customerInfo.entitlements.active.values.any(
          (e) => e.periodType == PeriodType.trial,
        );
        if (isInTrial) {
          Logger.debug('UserStateService: User is in trial period');
          return UserState.trialUser;
        } else {
          Logger.debug('UserStateService: User is pro (paid subscription)');
          return UserState.proUser;
        }
      }
      // 2. If no active entitlement, use eligibility data to differentiate NEW vs FREE
      else {
        Logger.debug(
          'UserStateService: No active entitlement, checking trial eligibility from provided map',
        );

        final isEligibleForAnyTrial = eligibility.values.any(
          (e) =>
              e.status == IntroEligibilityStatus.introEligibilityStatusEligible,
        );

        if (isEligibleForAnyTrial) {
          Logger.debug(
            'UserStateService: User is eligible for a trial -> newUser',
          );
          return UserState.newUser;
        } else {
          Logger.debug(
            'UserStateService: User is not eligible for a trial -> freeUser',
          );
          return UserState.freeUser;
        }
      }
    } catch (e) {
      Logger.error('UserStateService: Error determining user state', e);
      return UserState.unknown;
    }
  }

  /// Get user state asynchronously by fetching CustomerInfo
  /// Returns UserState.unknown if RevenueCat is not configured or on error
  Future<UserState> getUserStateAsync() async {
    try {
      // Check if RevenueCat is configured before calling getCustomerInfo
      if (!(await Purchases.isConfigured)) {
        Logger.debug(
          'UserStateService: Purchases SDK not configured yet. Returning unknown state.',
        );
        return UserState.unknown;
      }

      // Get customer info from RevenueCat
      final customerInfo = await Purchases.getCustomerInfo();

      // For backward compatibility, fetch eligibility here if called directly
      Map<String, IntroEligibility> eligibility = {};
      try {
        final offerings = await Purchases.getOfferings();
        final currentOffering = offerings.current;
        if (currentOffering != null &&
            currentOffering.availablePackages.isNotEmpty) {
          final productIds =
              currentOffering.availablePackages
                  .map((p) => p.storeProduct.identifier)
                  .toList();
          eligibility =
              await Purchases.checkTrialOrIntroductoryPriceEligibility(
                productIds,
              );
        }
      } catch (e) {
        Logger.error(
          'UserStateService: Error fetching eligibility for backward compatibility',
          e,
        );
        // Continue with empty eligibility map
      }

      return getUserState(customerInfo, eligibility);
    } catch (e) {
      Logger.error('UserStateService: Failed to get user state async', e);
      // In case of error, return unknown state
      return UserState.unknown;
    }
  }

  /// Check if user has full app access (trial or pro users)
  bool hasFullAccess(UserState userState) {
    return userState == UserState.trialUser || userState == UserState.proUser;
  }

  /// Check if user should see paywall immediately (new users)
  bool shouldShowPaywall(UserState userState) {
    // Changed: newUser should NOT see paywall immediately
    // They should be able to use the app with free tier limits
    // Paywall will be shown when they hit those limits
    return false;
  }

  /// Check if user has read-only access (free users)
  bool hasReadOnlyAccess(UserState userState) {
    return userState == UserState.freeUser;
  }

  /// Get user-friendly description of the user state
  String getUserStateDescription(UserState userState) {
    switch (userState) {
      case UserState.newUser:
        return 'New user - needs to start trial or subscribe';
      case UserState.trialUser:
        return 'Trial user - full access during trial period';
      case UserState.proUser:
        return 'Pro user - full paid access';
      case UserState.freeUser:
        return 'Free user - read-only access';
      case UserState.unknown:
        return 'Unknown user state';
    }
  }

  /// Legacy compatibility method - check if user is considered "pro"
  /// For backward compatibility with existing code
  bool isProUser(UserState userState) {
    return hasFullAccess(userState);
  }

  /// Legacy compatibility method - get user tier as string
  /// For backward compatibility with existing code
  String getUserTierString(UserState userState) {
    return hasFullAccess(userState) ? 'pro' : 'free';
  }
}
