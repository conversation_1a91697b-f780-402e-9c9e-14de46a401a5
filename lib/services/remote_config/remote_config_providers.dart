import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/remote_config/remote_config_service.dart';
import 'package:noeji/utils/logger.dart';

/// Provider for the Remote Config service
final remoteConfigServiceProvider = Provider<RemoteConfigService>((ref) {
  return RemoteConfigService.instance;
});

/// Provider that tracks when remote config is updated
/// This can be used to trigger rebuilds when config changes
final remoteConfigUpdateProvider = StreamProvider<void>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return remoteConfigService.onConfigUpdated;
});

/// Provider for LLM prompts that automatically updates when remote config changes
final llmPromptsProvider = Provider<LlmPrompts>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return LlmPrompts(remoteConfigService);
});

/// Provider for LLM model configurations that automatically updates when remote config changes
final llmModelConfigProvider = Provider<LlmModelConfig>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return LlmModelConfig(remoteConfigService);
});

/// Class that provides access to all LLM prompts from Remote Config
class LlmPrompts {
  final RemoteConfigService _remoteConfigService;

  LlmPrompts(this._remoteConfigService);

  /// Get the prompt for transcribing audio to create a new ideabook
  String getNewIdeabookPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_audio_transcription_new_ideabook_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for transcribing audio to create a new idea
  /// Variables: {{ideabook_name}}
  String getNewIdeaPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_audio_transcription_new_idea_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for transcribing audio for chat input
  String getChatInputPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_audio_transcription_chat_input_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for generating suggested prompts
  /// Variables: {{ideabook_name}}, {{formatted_ideas}}, {{previous_suggestions_text}}
  String getSuggestedPromptsPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_suggested_prompts_generation_prompt',
      userTier: userTier,
    );
  }

  /// Check if generative suggested prompts are enabled for the user tier
  bool isGenerativeSuggestedPromptsEnabled({String? userTier}) {
    final key =
        userTier == 'pro'
            ? 'pro_enable_generative_suggested_prompts'
            : 'free_enable_generative_suggested_prompts';
    return _remoteConfigService.getBool(key, userTier: userTier);
  }

  /// Get the chat system instruction template
  /// Variables: {{ideabook_name}}, {{ideas_text}}, {{verbosity}}, {{tone}}, {{output_format}}
  String getChatSystemInstruction({String? userTier}) {
    return _remoteConfigService.getString(
      'chat_system_instruction',
      userTier: userTier,
    );
  }

  /// Get the chat user instruction template
  /// Variables: {{user_content}}, {{previous_chat_messages}}
  String getChatUserInstruction({String? userTier}) {
    return _remoteConfigService.getString(
      'chat_user_instruction',
      userTier: userTier,
    );
  }

  /// Get the chat response style configuration as JSON
  Map<String, dynamic> getChatResponseStyle({String? userTier}) {
    final styleJson = _remoteConfigService.getString(
      'chat_response_style',
      userTier: userTier,
    );
    try {
      return json.decode(styleJson) as Map<String, dynamic>;
    } catch (e) {
      Logger.error('Error parsing chat response style JSON', e);
      // Return default style structure
      return {
        "verbosity": {
          "concise": {
            "prompt":
                "Concise: be brief and to the point. Max output: 1000 tokens",
            "description": "Be brief and to the point",
          },
          "balanced": {
            "prompt":
                "Balanced: provide a reasonable amount of detail without being overly lengthy or too short. Max output: 4000 tokens",
            "description":
                "Provide a reasonable amount of detail without being overly lengthy",
          },
          "verbose": {
            "prompt":
                "Verbose: provide detailed explanations, examples, and thorough information. Max output: 8000 tokens",
            "description":
                "Provide detailed explanations, examples, and thorough information",
          },
        },
        "tone": {
          "casual": {
            "prompt":
                "Casual: use a friendly, approachable, and conversational style. You can use contractions and more relaxed language. Use fun engaging emojis and emoticons unless user asks not to",
            "description": "Friendly, approachable and conversational",
          },
          "standard": {
            "prompt": "Standard: maintain a neutral, clear, and objective tone",
            "description": "Neutral, clear and objective",
          },
          "professional": {
            "prompt":
                "Professional: use formal language, complete sentences, and maintain a respectful, business-like demeanor",
            "description": "Formal, respectful and business-like",
          },
        },
        "output_format": {
          "free_form": {
            "prompt":
                "Free form: provide your response in well-structured paragraphs",
            "description": "Respond with well-structured paragraphs",
          },
          "structured": {
            "prompt":
                "Structured: present information using bullet points (for unordered items), numbered lists (for ordered steps or items), or a simple markdown table if the data is suitable for tabular presentation. Choose the most appropriate structured format for the content",
            "description":
                "Respond with bullet points, numbered lists and tables",
          },
        },
      };
    }
  }

  /// Get the default chat response style choices
  Map<String, String> getDefaultChatResponseStyle({String? userTier}) {
    final defaultsJson = _remoteConfigService.getString(
      'default_chat_response_style',
      userTier: userTier,
    );
    try {
      final defaults = json.decode(defaultsJson) as Map<String, dynamic>;
      return defaults.map((key, value) => MapEntry(key, value.toString()));
    } catch (e) {
      Logger.error('Error parsing default chat response style JSON', e);
      // Return default choices
      return {
        "verbosity": "balanced",
        "tone": "casual",
        "output_format": "free_form",
      };
    }
  }

  /// Replace variables in a prompt template
  String replaceVariables(String prompt, Map<String, String> variables) {
    String result = prompt;
    variables.forEach((key, value) {
      result = result.replaceAll('{{$key}}', value);
    });
    return result;
  }
}

/// Class that provides access to all LLM model configurations from Remote Config
class LlmModelConfig {
  final RemoteConfigService _remoteConfigService;

  LlmModelConfig(this._remoteConfigService);

  /// Get the model name for audio transcription
  String getAudioTranscriptionModel({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_audio_transcription_model_name',
      userTier: userTier,
    );
  }

  /// Get the model name for chat
  String getChatModel({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_chat_model_name',
      userTier: userTier,
    );
  }

  /// Get the model name for suggested prompts generation
  String getSuggestedPromptsModel({String? userTier}) {
    return _remoteConfigService.getString(
      'pro_suggested_prompts_model_name',
      userTier: userTier,
    );
  }


  /// Get the generation config for audio transcription
  Map<String, dynamic> getAudioTranscriptionConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'pro_audio_transcription_generation_config',
      userTier: userTier,
    );
  }

  /// Get the generation config for chat
  Map<String, dynamic> getChatConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'pro_chat_generation_config',
      userTier: userTier,
    );
  }

  /// Get the generation config for suggested prompts generation
  Map<String, dynamic> getSuggestedPromptsConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'pro_suggested_prompts_generation_config',
      userTier: userTier,
    );
  }

}
