import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:noeji/services/haptic/haptic_feedback_service.dart';
import 'package:noeji/utils/logger.dart';

/// Key for storing haptic feedback preference in SharedPreferences
const String _hapticFeedbackKey = 'haptic_feedback_enabled';

/// Firebase Remote Config key for default haptic feedback setting
const String _defaultHapticFeedbackRemoteConfigKey = 'default_enable_haptic_feedback';

/// Provider for haptic feedback enabled state
final hapticFeedbackProvider = StateNotifierProvider<HapticFeedbackNotifier, bool>((ref) {
  return HapticFeedbackNotifier();
});

/// Notifier for managing haptic feedback state with persistence
class HapticFeedbackNotifier extends StateNotifier<bool> {
  /// Constructor - initializes with default value and loads saved preference
  HapticFeedbackNotifier() : super(true) {
    _loadHapticFeedbackPreference();
  }
  
  /// Toggle haptic feedback enabled state
  void toggle() {
    final newState = !state;
    _setHapticFeedback(newState);
  }
  
  /// Set haptic feedback enabled state explicitly
  void setEnabled(bool enabled) {
    _setHapticFeedback(enabled);
  }
  
  /// Internal method to set haptic feedback state and persist it
  void _setHapticFeedback(bool enabled) {
    state = enabled;
    HapticFeedbackService.setEnabled(enabled);
    _saveHapticFeedbackPreference(enabled);
    Logger.debug('HapticFeedbackNotifier: Set haptic feedback to $enabled');
  }
  
  /// Load haptic feedback preference from storage or use default from remote config
  Future<void> _loadHapticFeedbackPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if user has a saved preference
      if (prefs.containsKey(_hapticFeedbackKey)) {
        final savedValue = prefs.getBool(_hapticFeedbackKey) ?? true;
        _setHapticFeedback(savedValue);
        Logger.debug('HapticFeedbackNotifier: Loaded saved preference: $savedValue');
        return;
      }
      
      // No saved preference, use default from Firebase Remote Config
      try {
        final remoteConfig = FirebaseRemoteConfig.instance;
        final defaultEnabled = remoteConfig.getBool(_defaultHapticFeedbackRemoteConfigKey);
        _setHapticFeedback(defaultEnabled);
        Logger.debug('HapticFeedbackNotifier: Using remote config default: $defaultEnabled');
      } catch (e) {
        // Fallback to true if remote config fails
        Logger.error('HapticFeedbackNotifier: Error reading remote config, using fallback default: true', e);
        _setHapticFeedback(true);
      }
    } catch (e) {
      Logger.error('HapticFeedbackNotifier: Error loading haptic feedback preference', e);
      // Use default value (true) on error
      _setHapticFeedback(true);
    }
  }
  
  /// Save haptic feedback preference to persistent storage
  Future<void> _saveHapticFeedbackPreference(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_hapticFeedbackKey, enabled);
      Logger.debug('HapticFeedbackNotifier: Saved preference: $enabled');
    } catch (e) {
      Logger.error('HapticFeedbackNotifier: Error saving haptic feedback preference', e);
    }
  }
}

/// Provider for logging haptic feedback state changes
final hapticFeedbackLoggerProvider = Provider<void>((ref) {
  ref.listen(hapticFeedbackProvider, (previous, next) {
    Logger.debug('HapticFeedbackProvider: State changed from $previous to $next');
  });
});