import 'package:flutter/services.dart';
import 'package:noeji/utils/logger.dart';

/// Enum for different haptic feedback intensities
enum HapticIntensity {
  /// Light feedback for subtle interactions
  light,

  /// Medium feedback for normal interactions
  medium,

  /// Strong feedback for important actions
  strong,

  /// Double strong feedback for very important actions
  doubleStrong,

  /// Selection feedback for UI selections
  selection,
}

/// Enum for different types of haptic feedback actions
enum HapticAction {
  // Recording actions
  recordingStart,
  recordingFinish,
  transcriptionReady,

  // Edit mode actions
  editModeEnter,
  editModeCancel,
  editModeSave,

  // Drag and drop actions
  dragStart,
  dragDrop,

  // Chat actions
  messageSend,
  aiResponseReady,
  saveAsNote,
  clearChat,

  // Clipboard actions
  copyToClipboard,

  // UI navigation actions
  floatingButtonExpand,
  floatingButtonCollapse,
  typeNewIdea,

  // Swipe actions
  swipeContextMenu,
  colorPicker,

  // Refresh actions
  refresh,

  // Generic actions
  buttonPress,
  itemSelect,
}

/// Configuration mapping actions to haptic intensities
class HapticFeedbackConfig {
  static const Map<HapticAction, HapticIntensity> _actionToIntensity = {
    // Recording actions - all strong
    HapticAction.recordingStart: HapticIntensity.strong,
    HapticAction.recordingFinish: HapticIntensity.strong,
    HapticAction.transcriptionReady: HapticIntensity.strong,

    // Edit mode actions - all strong
    HapticAction.editModeEnter: HapticIntensity.strong,
    HapticAction.editModeCancel: HapticIntensity.strong,
    HapticAction.editModeSave: HapticIntensity.strong,

    // Drag and drop actions - all strong
    HapticAction.dragStart: HapticIntensity.strong,
    HapticAction.dragDrop: HapticIntensity.strong,

    // Chat actions
    HapticAction.messageSend: HapticIntensity.strong,
    HapticAction.aiResponseReady: HapticIntensity.doubleStrong,
    HapticAction.saveAsNote: HapticIntensity.strong,
    HapticAction.clearChat: HapticIntensity.strong,

    // Clipboard actions - strong
    HapticAction.copyToClipboard: HapticIntensity.strong,

    // UI navigation actions - strong
    HapticAction.floatingButtonExpand: HapticIntensity.strong,
    HapticAction.floatingButtonCollapse: HapticIntensity.strong,
    HapticAction.typeNewIdea: HapticIntensity.strong,

    // Swipe actions - strong
    HapticAction.swipeContextMenu: HapticIntensity.strong,
    HapticAction.colorPicker: HapticIntensity.strong,

    // Refresh actions - strong
    HapticAction.refresh: HapticIntensity.strong,

    // Generic actions
    HapticAction.buttonPress: HapticIntensity.strong,
    HapticAction.itemSelect: HapticIntensity.strong,
  };

  /// Get the haptic intensity for a specific action
  static HapticIntensity getIntensity(HapticAction action) {
    return _actionToIntensity[action] ?? HapticIntensity.medium;
  }
}

/// Global haptic feedback service
class HapticFeedbackService {
  static bool _isEnabled = true;

  /// Set whether haptic feedback is enabled globally
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    Logger.debug('HapticFeedbackService: Enabled set to $enabled');
  }

  /// Get whether haptic feedback is currently enabled
  static bool get isEnabled => _isEnabled;

  /// Trigger haptic feedback for a specific action
  static Future<void> trigger(HapticAction action) async {
    if (!_isEnabled) {
      Logger.debug(
        'HapticFeedbackService: Haptic feedback disabled, skipping ${action.name}',
      );
      return;
    }

    final intensity = HapticFeedbackConfig.getIntensity(action);
    await _performHaptic(intensity);
    Logger.debug(
      'HapticFeedbackService: Triggered ${intensity.name} haptic for ${action.name}',
    );
  }

  /// Trigger haptic feedback with a specific intensity
  static Future<void> triggerWithIntensity(HapticIntensity intensity) async {
    if (!_isEnabled) {
      Logger.debug(
        'HapticFeedbackService: Haptic feedback disabled, skipping ${intensity.name}',
      );
      return;
    }

    await _performHaptic(intensity);
    Logger.debug('HapticFeedbackService: Triggered ${intensity.name} haptic');
  }

  /// Perform the actual haptic feedback based on intensity
  static Future<void> _performHaptic(HapticIntensity intensity) async {
    try {
      switch (intensity) {
        case HapticIntensity.light:
          await HapticFeedback.lightImpact();
          break;
        case HapticIntensity.medium:
          await HapticFeedback.mediumImpact();
          break;
        case HapticIntensity.strong:
          await HapticFeedback.heavyImpact();
          break;
        case HapticIntensity.doubleStrong:
          // Perform two heavy impacts with a short delay
          await HapticFeedback.heavyImpact();
          await Future.delayed(const Duration(milliseconds: 50));
          await HapticFeedback.heavyImpact();
          break;
        case HapticIntensity.selection:
          await HapticFeedback.selectionClick();
          break;
      }
    } catch (e) {
      Logger.error(
        'HapticFeedbackService: Error performing haptic feedback',
        e,
      );
    }
  }

  /// Convenience methods for common use cases

  /// Light haptic feedback
  static Future<void> light() async {
    await triggerWithIntensity(HapticIntensity.light);
  }

  /// Medium haptic feedback
  static Future<void> medium() async {
    await triggerWithIntensity(HapticIntensity.medium);
  }

  /// Strong haptic feedback
  static Future<void> strong() async {
    await triggerWithIntensity(HapticIntensity.strong);
  }

  /// Double strong haptic feedback
  static Future<void> doubleStrong() async {
    await triggerWithIntensity(HapticIntensity.doubleStrong);
  }

  /// Selection haptic feedback
  static Future<void> selection() async {
    await triggerWithIntensity(HapticIntensity.selection);
  }
}
