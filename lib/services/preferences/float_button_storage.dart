import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving floating button state preferences
class FloatButtonStorage {
  static const String _isExpandedKey = 'noeji_float_button_is_expanded';

  /// Save the floating button expanded state to persistent storage
  static Future<bool> saveIsExpanded(bool isExpanded) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_isExpandedKey, isExpanded);

      if (result) {
        Logger.debug(
          'Float button expanded state saved successfully: $isExpanded',
        );
      } else {
        Logger.error('Failed to save float button expanded state');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving float button expanded state', e);
      return false;
    }
  }

  /// Load the floating button expanded state from persistent storage
  /// Returns the default value if no preference is saved
  static Future<bool> loadIsExpanded({bool defaultValue = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(_isExpandedKey);

      if (value == null) {
        Logger.debug(
          'No float button expanded state found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Float button expanded state loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading float button expanded state', e);
      return defaultValue;
    }
  }

  /// Reset the floating button expanded state
  /// This is useful for testing or when user wants to reset to default
  static Future<bool> resetIsExpanded() async {
    try {
      Logger.debug('Attempting to reset float button expanded state');
      final prefs = await SharedPreferences.getInstance();

      final result = await prefs.remove(_isExpandedKey);

      if (result) {
        Logger.debug('Float button expanded state reset successfully');
      } else {
        Logger.error('Failed to reset float button expanded state');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting float button expanded state', e);
      return false;
    }
  }
}