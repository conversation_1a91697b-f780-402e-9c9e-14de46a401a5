import 'dart:convert';
import 'dart:io';
import 'package:firebase_ai/firebase_ai.dart';
import 'package:noeji/services/llm/firebase_ai_provider.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/retry_util.dart';

/// Base class for LLM service responses
abstract class LlmResponse {
  /// Whether the response was successful
  final bool isSuccess;

  /// Error message if the response was not successful
  final String? errorMessage;

  /// Constructor
  const LlmResponse({required this.isSuccess, this.errorMessage});
}

/// Response from transcribing audio to text
class TranscriptionResponse extends LlmResponse {
  /// The short name extracted from the transcription (for new ideabook)
  final String? shortName;

  /// The color selected for the ideabook (for new ideabook)
  final String? color;

  /// Whether the ideabook should be displayed as a todo list (for new ideabook)
  final bool? isTodo;

  /// The idea content extracted from the transcription (for new idea)
  final String? idea;

  /// The short title extracted from the transcription (for new idea)
  final String? shortTitle;

  /// The transcript content extracted from the transcription (for chat input)
  final String? transcript;

  /// Constructor for successful ideabook response
  const TranscriptionResponse.successIdeabook({
    required this.shortName,
    this.color,
    this.isTodo,
  }) : idea = null,
       shortTitle = null,
       transcript = null,
       super(isSuccess: true);

  /// Constructor for successful idea response
  const TranscriptionResponse.successIdea({required this.idea, this.shortTitle})
    : shortName = null,
      color = null,
      isTodo = null,
      transcript = null,
      super(isSuccess: true);

  /// Constructor for successful chat input response
  const TranscriptionResponse.successChatInput({required this.transcript})
    : shortName = null,
      color = null,
      isTodo = null,
      idea = null,
      shortTitle = null,
      super(isSuccess: true);

  /// Constructor for failed response
  const TranscriptionResponse.failure({required String errorMessage})
    : shortName = null,
      color = null,
      isTodo = null,
      idea = null,
      shortTitle = null,
      transcript = null,
      super(isSuccess: false, errorMessage: errorMessage);
}

/// Enum for different transcription use cases
enum TranscriptionUseCase {
  /// Transcribe audio for creating a new ideabook
  newIdeabook,

  /// Transcribe audio for adding a new idea to an existing ideabook
  newIdea,

  /// Transcribe audio for chat input
  chatInput,
}

/// Interface for LLM services
abstract class LlmService {
  /// Transcribe audio to text
  Future<TranscriptionResponse> transcribeAudio(
    String audioFilePath, {
    TranscriptionUseCase useCase = TranscriptionUseCase.newIdeabook,
    String? ideabookName,
  });
}

/// Implementation of LLM service using Google Gemini API via Firebase AI Logic SDK
class GeminiService implements LlmService {
  /// The Firebase AI provider
  final FirebaseAiProvider _firebaseAiProvider;

  /// LLM prompts provider
  final LlmPrompts _llmPrompts;

  /// LLM model config provider
  final LlmModelConfig _llmModelConfig;

  /// User tier for accessing appropriate prompts and configs
  final String? _userTier;

  /// Constructor
  GeminiService({
    FirebaseAiProvider? firebaseAiProvider,
    required LlmPrompts llmPrompts,
    required LlmModelConfig llmModelConfig,
    String? userTier,
  }) : _firebaseAiProvider = firebaseAiProvider ?? FirebaseAiProvider(),
       _llmPrompts = llmPrompts,
       _llmModelConfig = llmModelConfig,
       _userTier = userTier;

  @override
  Future<TranscriptionResponse> transcribeAudio(
    String audioFilePath, {
    TranscriptionUseCase useCase = TranscriptionUseCase.newIdeabook,
    String? ideabookName,
  }) async {
    // Check if the file exists first - this is a non-retryable error
    final file = File(audioFilePath);
    if (!await file.exists()) {
      return const TranscriptionResponse.failure(
        errorMessage: 'Audio file does not exist',
      );
    }

    // Use retry utility for the transcription operation
    try {
      return await RetryUtil.retry<TranscriptionResponse>(
        operation:
            () => _performTranscription(audioFilePath, useCase, ideabookName),
        maxRetries: 3,
        initialDelay: 500,
        maxDelay: 5000,
        operationName: 'transcribeAudio (${useCase.name})',
      );
    } catch (e) {
      Logger.error('All retries failed for transcription', e);
      return TranscriptionResponse.failure(
        errorMessage: 'Failed to transcribe audio after multiple attempts: $e',
      );
    }
  }

  /// Perform the actual transcription operation
  /// This is separated from the main method to allow for retries
  Future<TranscriptionResponse> _performTranscription(
    String audioFilePath,
    TranscriptionUseCase useCase,
    String? ideabookName,
  ) async {
    try {
      final fileSize = await File(audioFilePath).length();
      Logger.debug('Audio file size: $fileSize bytes');

      // Check file size limit (20MB after base64 encoding, so ~15MB raw)
      if (fileSize > 15 * 1024 * 1024) {
        Logger.debug(
          'Audio file size is large ($fileSize bytes), might exceed 20MB request limit after base64 encoding.',
        );
      }

      // Read the file as bytes
      final bytes = await File(audioFilePath).readAsBytes();

      // Get the appropriate prompt based on the use case
      final promptText = _getPromptForUseCase(useCase, ideabookName);

      // Get model name and generation config from Remote Config
      final modelName = _llmModelConfig.getAudioTranscriptionModel(
        userTier: _userTier,
      );
      final remoteConfigMap = _llmModelConfig.getAudioTranscriptionConfig(
        userTier: _userTier,
      );

      // Create GenerationConfig from remote config
      final baseGenerationConfig = GenerationConfig(
        candidateCount: remoteConfigMap['candidateCount'] as int?,
        maxOutputTokens: remoteConfigMap['maxOutputTokens'] as int?,
        temperature: remoteConfigMap['temperature'] as double?,
        topP: remoteConfigMap['topP'] as double?,
        topK: remoteConfigMap['topK'] as int?,
      );

      // Get the response schema for this use case
      final schema = _firebaseAiProvider.getSchemaForTranscriptionUseCase(
        useCase,
      );

      // Get the model configured for audio transcription
      final model = _firebaseAiProvider.getAudioTranscriptionModel(
        modelName: modelName,
        responseSchema: schema,
        baseGenerationConfig: baseGenerationConfig,
      );

      // Create the content parts for the request
      final audioPart = InlineDataPart('audio/aac', bytes);
      final promptPart = TextPart(promptText);

      Logger.debug(
        'Calling Firebase AI Logic SDK for transcription with:\nModel ${model.toString()}\nPrompt: $promptText',
      );

      // Call the Firebase AI Logic SDK
      final response = await model.generateContent([
        Content.multi([promptPart, audioPart]),
      ]);

      Logger.debug('Firebase AI Logic SDK response: ${response.text}');

      if (response.text == null || response.text!.isEmpty) {
        throw Exception('No text in response from Firebase AI Logic SDK');
      }

      final String jsonString = response.text!;
      // The SDK with responseSchema should directly give us valid JSON string
      final dynamic parsedJson = jsonDecode(jsonString);
      Logger.debug('Parsed JSON type: ${parsedJson.runtimeType}');
      Logger.debug('Parsed JSON content: $parsedJson');

      // Handle different response formats
      Map<String, dynamic> jsonData;

      if (parsedJson is List) {
        // If it's an array, take the first item
        if (parsedJson.isEmpty) {
          throw Exception('Empty JSON array in response');
        }
        jsonData = parsedJson[0] as Map<String, dynamic>;
        Logger.debug('Extracted JSON data from array: $jsonData');
      } else if (parsedJson is Map<String, dynamic>) {
        // If it's already a map, use it directly
        jsonData = parsedJson;
        Logger.debug('Using JSON data directly: $jsonData');
      } else {
        throw Exception('Unexpected JSON format: ${parsedJson.runtimeType}');
      }

      // Process the response based on the use case
      return _processResponseForUseCase(useCase, jsonData);
    } catch (e) {
      Logger.error('Error in SDK transcription operation', e);
      // Map SDK errors to your existing error handling or rethrow
      if (e is FirebaseAIException) {
        throw Exception('Firebase AI SDK error: ${e.message}');
      }
      rethrow;
    }
  }

  /// Get the appropriate prompt text based on the use case
  String _getPromptForUseCase(
    TranscriptionUseCase useCase,
    String? ideabookName,
  ) {
    switch (useCase) {
      case TranscriptionUseCase.newIdeabook:
        return _llmPrompts.getNewIdeabookPrompt(userTier: _userTier);
      case TranscriptionUseCase.newIdea:
        // Get the prompt template and replace variables
        final promptTemplate = _llmPrompts.getNewIdeaPrompt(
          userTier: _userTier,
        );
        final bookName = ideabookName ?? "My Ideabook";
        return _llmPrompts.replaceVariables(promptTemplate, {
          'ideabook_name': bookName,
        });
      case TranscriptionUseCase.chatInput:
        return _llmPrompts.getChatInputPrompt(userTier: _userTier);
    }
  }

  /// Process the response based on the use case
  TranscriptionResponse _processResponseForUseCase(
    TranscriptionUseCase useCase,
    Map<String, dynamic> jsonData,
  ) {
    Logger.debug('Processing response for use case: $useCase, data: $jsonData');

    switch (useCase) {
      case TranscriptionUseCase.newIdeabook:
        // Check if the required field is present
        if (!jsonData.containsKey('short_name')) {
          return TranscriptionResponse.failure(
            errorMessage:
                'Missing required field "short_name" in response: $jsonData',
          );
        }

        // Get the color if present, validate it's one of the expected values
        String? color;
        if (jsonData.containsKey('color')) {
          final colorValue = jsonData['color'].toString().toLowerCase();
          // Check if it's a valid color
          if ([
            'red',
            'green',
            'blue',
            'yellow',
            'orange',
            'purple',
          ].contains(colorValue)) {
            color = colorValue;
            Logger.debug('Color selected for ideabook: $color');
          } else {
            Logger.debug(
              'Invalid color value in response: $colorValue, using default',
            );
          }
        } else {
          Logger.debug('No color field in response, using default');
        }

        // Get the is_todo flag if present, validate it's Y or N
        bool? isTodo;
        if (jsonData.containsKey('is_todo')) {
          final isTodoValue = jsonData['is_todo'].toString();
          // Convert Y/N to boolean
          if (isTodoValue == 'Y') {
            isTodo = true;
            Logger.debug('Ideabook should be displayed as todo list: $isTodo');
          } else if (isTodoValue == 'N') {
            isTodo = false;
            Logger.debug('Ideabook should be displayed as regular list: $isTodo');
          } else {
            Logger.debug(
              'Invalid is_todo value in response: $isTodoValue, using default (false)',
            );
            isTodo = false;
          }
        } else {
          Logger.debug('No is_todo field in response, using default (false)');
          isTodo = false;
        }

        return TranscriptionResponse.successIdeabook(
          shortName: jsonData['short_name'],
          color: color,
          isTodo: isTodo,
        );

      case TranscriptionUseCase.newIdea:
        // Check if the required fields are present
        if (!jsonData.containsKey('idea')) {
          return TranscriptionResponse.failure(
            errorMessage:
                'Missing required field "idea" in response: $jsonData',
          );
        }

        // Get the short title if present
        String? shortTitle;
        if (jsonData.containsKey('short_title')) {
          shortTitle = jsonData['short_title'];
          Logger.debug('Short title for idea: $shortTitle');
        } else {
          Logger.debug('No short_title field in response, using default');
        }

        Logger.debug(
          'Creating idea response with content: ${jsonData['idea']}',
        );
        return TranscriptionResponse.successIdea(
          idea: jsonData['idea'],
          shortTitle: shortTitle,
        );

      case TranscriptionUseCase.chatInput:
        // Check if the required field is present
        if (!jsonData.containsKey('transcript')) {
          return TranscriptionResponse.failure(
            errorMessage:
                'Missing required field "transcript" in response: $jsonData',
          );
        }

        Logger.debug(
          'Creating chat input response with content: ${jsonData['transcript']}',
        );
        return TranscriptionResponse.successChatInput(
          transcript: jsonData['transcript'],
        );
    }
  }

  /// Dispose method (kept for backward compatibility)
  void dispose() {
    // No HTTP client to dispose anymore
  }
}
