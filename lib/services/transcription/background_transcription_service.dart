import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/llm/llm_service.dart';
import 'package:noeji/services/llm/llm_providers.dart';
import 'package:noeji/services/firebase/firestore_service.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Service for handling background transcription of audio files
class BackgroundTranscriptionService {
  final LlmService _llmService;
  final FirestoreService _firestoreService;

  /// Constructor
  BackgroundTranscriptionService({
    required LlmService llmService,
    required FirestoreService firestoreService,
  }) : _llmService = llmService,
       _firestoreService = firestoreService;

  /// Start background transcription for an idea with audio file
  /// This is a "fire and forget" operation - the caller doesn't await the result
  void startBackgroundTranscription({
    required String ideabookId,
    required String ideaId,
    required String audioFilePath,
    String? ideabookName,
    int maxRetries = 3,
  }) {
    // Start the transcription process but don't await it
    _performBackgroundTranscription(
      ideabookId: ideabookId,
      ideaId: ideaId,
      audioFilePath: audioFilePath,
      ideabookName: ideabookName,
      maxRetries: maxRetries,
    ).catchError((error) {
      // Log the error but don't rethrow since this is a background operation
      Logger.error(
        'Background transcription failed for idea $ideaId: $error',
        error,
      );
    });
  }

  /// Perform the actual background transcription with retries
  Future<void> _performBackgroundTranscription({
    required String ideabookId,
    required String ideaId,
    required String audioFilePath,
    String? ideabookName,
    int maxRetries = 3,
  }) async {
    Logger.debug(
      'Starting background transcription for idea $ideaId with audio file: $audioFilePath',
    );

    // Verify the audio file still exists
    final audioFile = File(audioFilePath);
    if (!await audioFile.exists()) {
      Logger.error(
        'Audio file does not exist for background transcription: $audioFilePath',
      );
      await _markTranscriptionFailed(ideabookId, ideaId);
      return;
    }

    int attempts = 0;
    while (attempts < maxRetries) {
      attempts++;

      try {
        Logger.debug(
          'Background transcription attempt $attempts/$maxRetries for idea $ideaId',
        );

        // Attempt transcription
        final transcriptionResult = await _llmService.transcribeAudio(
          audioFilePath,
          useCase: TranscriptionUseCase.newIdea,
          ideabookName: ideabookName,
        );

        if (transcriptionResult.isSuccess && transcriptionResult.idea != null) {
          // Transcription succeeded, update the idea
          await _updateIdeaWithTranscription(
            ideabookId,
            ideaId,
            transcriptionResult.idea!,
            audioFilePath,
          );
          Logger.debug(
            'Background transcription completed successfully for idea $ideaId',
          );
          return;
        } else {
          Logger.error(
            'Background transcription failed for idea $ideaId: ${transcriptionResult.errorMessage}',
          );

          // If this was the last attempt, mark as failed
          if (attempts >= maxRetries) {
            await _markTranscriptionFailed(ideabookId, ideaId);
            return;
          }

          // Wait before retrying (exponential backoff)
          await Future.delayed(Duration(seconds: attempts * 2));
        }
      } catch (e) {
        Logger.error(
          'Background transcription attempt $attempts failed for idea $ideaId',
          e,
        );

        // If this was the last attempt, mark as failed
        if (attempts >= maxRetries) {
          await _markTranscriptionFailed(ideabookId, ideaId);
          return;
        }

        // Wait before retrying (exponential backoff)
        await Future.delayed(Duration(seconds: attempts * 2));
      }
    }
  }

  /// Update the idea with successful transcription results
  Future<void> _updateIdeaWithTranscription(
    String ideabookId,
    String ideaId,
    String transcribedContent,
    String audioFilePath,
  ) async {
    try {
      // Get the current idea
      final currentIdea =
          await _firestoreService.listenToIdeaById(ideabookId, ideaId).first;

      if (currentIdea == null) {
        Logger.error(
          'Cannot update transcription for idea $ideaId: idea not found',
        );
        return;
      }

      // Create updated idea with transcription content and remove audio file reference
      Logger.debug(
        'BEFORE copyWith: currentIdea.audioFileName = ${currentIdea.audioFileName}',
      );
      final updatedIdea = currentIdea.copyWith(
        content: transcribedContent,
        audioFileName: null, // Remove the audio file name to save storage
        transcriptionError: false, // Clear any previous error state
      );
      Logger.debug(
        'AFTER copyWith: updatedIdea.audioFileName = ${updatedIdea.audioFileName}',
      );

      // Update in Firestore
      final success = await _firestoreService.updateIdea(
        ideabookId,
        updatedIdea,
      );

      if (success) {
        Logger.debug(
          'Successfully updated idea $ideaId with transcription content',
        );

        // Clean up the audio file to save storage
        try {
          final audioFile = File(audioFilePath);
          if (await audioFile.exists()) {
            await audioFile.delete();
            Logger.debug('Cleaned up audio file: $audioFilePath');
          }
        } catch (e) {
          Logger.error('Failed to clean up audio file: $audioFilePath', e);
          // Don't fail the whole operation if file cleanup fails
        }
      } else {
        Logger.error(
          'Failed to update idea $ideaId with transcription content',
        );
      }
    } catch (e) {
      Logger.error('Error updating idea $ideaId with transcription content', e);
    }
  }

  /// Mark an idea as having a transcription error
  Future<void> _markTranscriptionFailed(
    String ideabookId,
    String ideaId,
  ) async {
    try {
      // Get the current idea
      final currentIdea =
          await _firestoreService.listenToIdeaById(ideabookId, ideaId).first;

      if (currentIdea == null) {
        Logger.error(
          'Cannot mark transcription failed for idea $ideaId: idea not found',
        );
        return;
      }

      // Create updated idea with error flag set
      final updatedIdea = currentIdea.copyWith(
        transcriptionError: true,
        // Keep the audio file name so user can retry
      );

      // Update in Firestore
      final success = await _firestoreService.updateIdea(
        ideabookId,
        updatedIdea,
      );

      if (success) {
        Logger.debug('Marked idea $ideaId as transcription failed');
      } else {
        Logger.error('Failed to mark idea $ideaId as transcription failed');
      }
    } catch (e) {
      Logger.error('Error marking idea $ideaId as transcription failed', e);
    }
  }

  /// Retry transcription for a failed idea
  Future<void> retryTranscription({
    required String ideabookId,
    required String ideaId,
    String? ideabookName,
  }) async {
    try {
      // Get the current idea
      final currentIdea =
          await _firestoreService.listenToIdeaById(ideabookId, ideaId).first;

      if (currentIdea == null) {
        Logger.error(
          'Cannot retry transcription for idea $ideaId: idea not found',
        );
        return;
      }

      if (currentIdea.audioFileName == null ||
          currentIdea.audioFileName!.isEmpty) {
        Logger.error(
          'Cannot retry transcription for idea $ideaId: no audio file available',
        );
        return;
      }

      // Reset the error state and start transcription again
      final resetIdea = currentIdea.copyWith(transcriptionError: false);

      // Update in Firestore first to clear the error state
      await _firestoreService.updateIdea(ideabookId, resetIdea);

      // Start background transcription again
      startBackgroundTranscription(
        ideabookId: ideabookId,
        ideaId: ideaId,
        audioFilePath: currentIdea.audioFileName!,
        ideabookName: ideabookName,
      );

      Logger.debug('Retrying transcription for idea $ideaId');
    } catch (e) {
      Logger.error('Error retrying transcription for idea $ideaId', e);
      // Mark as failed again if the retry setup fails
      await _markTranscriptionFailed(ideabookId, ideaId);
    }
  }
}

/// Provider for BackgroundTranscriptionService
final backgroundTranscriptionServiceProvider =
    Provider<BackgroundTranscriptionService>((ref) {
      final llmService = ref.watch(llmServiceProvider);
      final firestoreService = ref.watch(firestoreServiceProvider);

      return BackgroundTranscriptionService(
        llmService: llmService,
        firestoreService: firestoreService,
      );
    });
