import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:noeji/models/chat_rate_limit.dart';
import 'package:noeji/services/preferences/chat_rate_limit_storage.dart';
import 'package:noeji/utils/logger.dart';

/// A write-through cache for chat rate limiting
/// This service ensures that both in-memory cache and persistent storage
/// are always in sync, preventing race conditions and ensuring accurate rate limiting
class ChatRateLimitCache extends ChangeNotifier {
  /// In-memory cache of message timestamps
  List<DateTime> _messageTimes = [];

  /// Lock to prevent concurrent modifications to the cache
  final _cacheLock = Object();

  /// TTL for message logs (45 days)
  static const Duration messageTtl = ChatRateLimitStorage.messageTtl;

  /// Whether the cache has been initialized
  bool _isInitialized = false;

  /// Completer for tracking initialization
  final Completer<void> _initCompleter = Completer<void>();

  /// Future that completes when the cache is initialized
  Future<void> get initialized => _initCompleter.future;

  /// Flag to track if the cache has been disposed
  bool _isDisposed = false;

  /// Constructor
  ChatRateLimitCache() {
    _initialize();
  }

  /// Initialize the cache from storage
  Future<void> _initialize() async {
    if (_isInitialized || _isDisposed) return;

    Logger.debug('Initializing chat rate limit cache');
    try {
      final log = await ChatRateLimitStorage.loadMessageLog();

      synchronized(_cacheLock, () {
        _messageTimes = List<DateTime>.from(log.messageTimes);
        Logger.debug(
          'Chat rate limit cache initialized with ${_messageTimes.length} messages',
        );
      });

      _isInitialized = true;
      _initCompleter.complete();

      // Only notify listeners if not disposed
      if (!_isDisposed) {
        notifyListeners();
      }
    } catch (e) {
      Logger.error('Error initializing chat rate limit cache', e);
      // Complete with error to avoid hanging
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
    }
  }

  /// Get a copy of the current message times
  List<DateTime> get messageTimes {
    return synchronized(_cacheLock, () {
      return List<DateTime>.from(_messageTimes);
    });
  }

  /// Add a new message to the cache and storage
  /// Returns true if the message was added successfully
  Future<bool> addMessage(DateTime timestamp) async {
    // Don't add messages if disposed
    if (_isDisposed) return false;

    Logger.debug('===== ADDING MESSAGE TO RATE LIMIT CACHE =====');
    Logger.debug('New message timestamp: ${timestamp.toIso8601String()}');

    // Wait for initialization to complete
    if (!_isInitialized) {
      Logger.debug('Waiting for cache initialization before adding message');
      await initialized;
    }

    // Check again if disposed after waiting
    if (_isDisposed) return false;

    // Add to in-memory cache immediately
    synchronized(_cacheLock, () {
      _messageTimes.add(timestamp);
      Logger.debug(
        'Added message to in-memory cache (now ${_messageTimes.length} messages)',
      );
    });

    // Clean up old messages in memory
    _cleanupOldMessages();

    // Save to persistent storage
    try {
      final log = ChatMessageLog(
        messageTimes: messageTimes,
        updatedAt: DateTime.now(),
      );

      final result = await ChatRateLimitStorage.saveMessageLog(log);
      Logger.debug('Message saved to persistent storage: $result');

      // Notify listeners after successful save only if not disposed
      if (result && !_isDisposed) {
        notifyListeners();
      }

      Logger.debug('===== MESSAGE ADDED TO RATE LIMIT CACHE =====');
      return result;
    } catch (e) {
      Logger.error('Error saving message to persistent storage', e);
      return false;
    }
  }

  /// Clean up old messages based on TTL
  void _cleanupOldMessages() {
    final cutoffTime = DateTime.now().subtract(messageTtl);

    synchronized(_cacheLock, () {
      final originalCount = _messageTimes.length;
      _messageTimes.removeWhere((time) => time.isBefore(cutoffTime));

      final removedCount = originalCount - _messageTimes.length;
      if (removedCount > 0) {
        Logger.debug('Removed $removedCount old messages from in-memory cache');
      }
    });
  }

  /// Clear all messages from the cache and storage
  Future<bool> clearAll() async {
    // Don't clear if disposed
    if (_isDisposed) return false;

    Logger.debug('===== CLEARING RATE LIMIT CACHE =====');

    // Clear in-memory cache
    synchronized(_cacheLock, () {
      _messageTimes.clear();
      Logger.debug('In-memory cache cleared');
    });

    // Clear persistent storage
    try {
      final result = await ChatRateLimitStorage.clearMessageLog();
      Logger.debug('Persistent storage cleared: $result');

      if (result && !_isDisposed) {
        notifyListeners();
      }

      Logger.debug('===== RATE LIMIT CACHE CLEARED =====');
      return result;
    } catch (e) {
      Logger.error('Error clearing persistent storage', e);
      return false;
    }
  }

  /// Force refresh the cache from storage
  /// This is useful when the app is resumed from background
  Future<void> forceRefresh() async {
    // Don't refresh if disposed
    if (_isDisposed) return;

    Logger.debug('===== FORCE REFRESHING RATE LIMIT CACHE =====');

    try {
      final log = await ChatRateLimitStorage.loadMessageLog();

      synchronized(_cacheLock, () {
        _messageTimes = List<DateTime>.from(log.messageTimes);
        Logger.debug('Cache refreshed with ${_messageTimes.length} messages');
      });

      if (!_isDisposed) {
        notifyListeners();
      }
      Logger.debug('===== RATE LIMIT CACHE REFRESHED =====');
    } catch (e) {
      Logger.error('Error refreshing rate limit cache', e);
    }
  }

  /// Set message times directly and save to storage
  /// This is used during migration to avoid duplicating messages
  Future<bool> setMessageTimes(List<DateTime> times) async {
    // Don't set if disposed
    if (_isDisposed) return false;

    Logger.debug('===== SETTING MESSAGE TIMES DIRECTLY =====');
    Logger.debug('Setting ${times.length} message times');

    // Wait for initialization to complete
    if (!_isInitialized) {
      Logger.debug(
        'Waiting for cache initialization before setting message times',
      );
      await initialized;
    }

    // Check again if disposed after waiting
    if (_isDisposed) return false;

    // Set in-memory cache
    synchronized(_cacheLock, () {
      _messageTimes = List<DateTime>.from(times);
      Logger.debug('Set in-memory cache to ${_messageTimes.length} messages');
    });

    // Clean up old messages in memory
    _cleanupOldMessages();

    // Save to persistent storage
    try {
      final log = ChatMessageLog(
        messageTimes: messageTimes,
        updatedAt: DateTime.now(),
      );

      final result = await ChatRateLimitStorage.saveMessageLog(log);
      Logger.debug('Message times saved to persistent storage: $result');

      // Notify listeners after successful save only if not disposed
      if (result && !_isDisposed) {
        notifyListeners();
      }

      Logger.debug('===== MESSAGE TIMES SET SUCCESSFULLY =====');
      return result;
    } catch (e) {
      Logger.error('Error saving message times to persistent storage', e);
      return false;
    }
  }

  /// Synchronized execution of a function with a lock
  T synchronized<T>(Object lock, T Function() fn) {
    // Simple synchronization using a lock object
    // In a real implementation, this would use a proper mutex
    // but for our purposes, this is sufficient
    return fn();
  }

  @override
  void dispose() {
    if (_isDisposed) return;

    _isDisposed = true;
    super.dispose();
  }
}
