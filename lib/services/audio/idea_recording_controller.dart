import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/audio/audio_recording_controller.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/services/firebase/firestore_listener_pool.dart';
import 'package:noeji/services/transcription/background_transcription_service.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/ideabook_notification_provider.dart';
import 'package:noeji/ui/providers/ideabook_recording_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Controller for recording audio for new ideas
class IdeaRecordingController extends BaseAudioRecordingController {
  /// The ideabook ID for which the idea is being recorded
  final String ideabookId;

  /// Constructor
  IdeaRecordingController({required this.ideabookId});

  @override
  String get controllerName => 'IdeaRecordingController';

  @override
  Future<void> handleRecordingCompleted(
    WidgetRef ref,
    String filePath,
    Duration duration,
  ) async {
    Logger.debug(
      '$controllerName: Recording completed for ideabook $ideabookId: $filePath, duration: ${duration.inSeconds}s',
    );

    // Set the state to processing temporarily
    beginAudioProcessing(ref);

    try {
      // Get the ideabook name for future transcription
      String? ideabookName;
      try {
        final listenerPool = safelyUseRef(
          ref,
          (r) => r.read(firestoreListenerPoolProvider),
        );
        if (listenerPool != null) {
          final ideabook =
              await listenerPool.getIdeabookStream(ideabookId).first;
          if (ideabook != null) {
            ideabookName = ideabook.name;
            Logger.debug('$controllerName: Using ideabook name: $ideabookName');
          }
        }
      } catch (e) {
        Logger.error('$controllerName: Error getting ideabook name', e);
      }

      // Immediately save a placeholder idea with audio file reference
      Logger.debug(
        '$controllerName: Creating placeholder idea with audio file: $filePath',
      );

      final firestoreService = safelyUseRef(
        ref,
        (r) => r.read(firestoreServiceProvider),
      );

      if (firestoreService == null) {
        Logger.error(
          '$controllerName: Could not access Firestore service - widget may be disposed',
        );
        return;
      }

      // Create placeholder idea with empty content and audio file reference
      final placeholderIdea = Idea(
        id: '', // Will be set by Firestore
        content: '', // Empty content initially
        audioFileName: filePath, // Store the audio file path
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the placeholder idea and await completion for faster response
      final newIdea = await firestoreService.createIdea(
        ideabookId,
        placeholderIdea,
      );

      Logger.debug(
        '$controllerName: Placeholder idea created with ID: ${newIdea.id}',
      );

      // Show generic success notification immediately
      showNotification(ref, 'Idea captured! 💡');

      // Immediately reset processing state and exit recording mode for fast response
      completeAudioProcessing(ref);
      safelyUseRef(ref, (r) {
        r.read(ideabookRecordingProvider.notifier).state = null;
      });

      // Start background transcription (fire and forget)
      final backgroundTranscriptionService = safelyUseRef(
        ref,
        (r) => r.read(backgroundTranscriptionServiceProvider),
      );

      if (backgroundTranscriptionService != null) {
        backgroundTranscriptionService.startBackgroundTranscription(
          ideabookId: ideabookId,
          ideaId: newIdea.id,
          audioFilePath: filePath,
          ideabookName: ideabookName,
        );

        Logger.debug(
          '$controllerName: Background transcription started for idea ${newIdea.id}',
        );
      } else {
        Logger.error(
          '$controllerName: Could not access background transcription service',
        );
        // TODO: Mark the idea as failed since we can't transcribe it
      }

      Logger.debug(
        '$controllerName: Fast response completed, background transcription in progress',
      );
    } catch (e) {
      // Handle any unexpected errors
      Logger.error('$controllerName: Error processing recording', e);

      // Handle specific error cases
      String errorMessage;
      if (e.toString().contains('ideas reached')) {
        errorMessage =
            'Ideabook is full! Create a new ideabook to add more ideas.';
      } else {
        errorMessage = 'Error saving recording - please try again';
      }

      // Show error notification
      showNotification(ref, errorMessage);

      // Reset the processing state and exit recording mode
      completeAudioProcessing(ref);
      safelyUseRef(ref, (r) {
        r.read(ideabookRecordingProvider.notifier).state = null;
      });
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug(
      '$controllerName: Recording cancelled for ideabook $ideabookId',
    );

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error(
      '$controllerName: Recording failed for ideabook $ideabookId: $errorMessage',
    );

    // Show error notification
    showNotification(ref, 'Recording failed');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error(
      '$controllerName: Microphone permission denied for ideabook $ideabookId',
    );

    // Show permission error notification
    showNotification(ref, 'Microphone permission required');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideabookRecordingProvider.notifier).state = null;
    });
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Use the ideabook notification provider to show inline notifications
    safelyUseRef(ref, (r) {
      r.read(showIdeabookNotificationProvider)(ideabookId, message);
    });
  }
}

/// Provider for the idea recording controller
final ideaRecordingControllerProvider =
    Provider.family<IdeaRecordingController, String>((ref, ideabookId) {
      return IdeaRecordingController(ideabookId: ideabookId);
    });
