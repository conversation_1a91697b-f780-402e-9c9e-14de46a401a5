package com.rokabyte.noeji

import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintManager
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.content.FileProvider
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import java.io.File
import java.io.FileOutputStream
import java.io.FileWriter
import java.io.IOException
import android.os.Handler
import android.os.Looper
import android.graphics.pdf.PdfDocument
import android.graphics.Canvas
import android.graphics.Paint
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.Log

class MainActivity : FlutterFragmentActivity() {
    private val BACK_BUTTON_CHANNEL = "com.rokabyte.noeji/back_button"
    private val FILE_EXPORT_CHANNEL = "com.noeji.app/file_export"
    private var backButtonHandler: (() -> Boolean)? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)

        // Back button handler
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, BACK_BUTTON_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setBackButtonHandler" -> {
                    val isInChatOrNotesTab = call.argument<Boolean>("isInChatOrNotesTab") ?: false
                    backButtonHandler = if (isInChatOrNotesTab) {
                        {
                            // Return true to indicate we've handled the back press
                            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, BACK_BUTTON_CHANNEL)
                                .invokeMethod("onBackPressed", null)
                            true
                        }
                    } else {
                        null
                    }
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }

        // File export handler
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, FILE_EXPORT_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "saveMarkdownFile" -> {
                    val filename = call.argument<String>("filename") ?: "export.md"
                    val content = call.argument<String>("content") ?: ""
                    val success = saveMarkdownFile(filename, content)
                    result.success(success)
                }
                "saveHtmlFile" -> {
                    val filename = call.argument<String>("filename") ?: "export.html"
                    val content = call.argument<String>("content") ?: ""
                    val success = saveHtmlFile(filename, content)
                    result.success(success)
                }
                "savePdfFile" -> {
                    val filename = call.argument<String>("filename") ?: "export.pdf"
                    val htmlContent = call.argument<String>("htmlContent") 
                        ?: call.argument<String>("markdownContent") ?: "" // Support both parameter names for backward compatibility
                    savePdfFile(filename, htmlContent, result)
                }
                else -> result.notImplemented()
            }
        }
    }

    override fun onBackPressed() {
        val handled = backButtonHandler?.invoke() ?: false
        if (!handled) {
            super.onBackPressed()
        }
    }

    private fun saveMarkdownFile(filename: String, content: String): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_CREATE_DOCUMENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "text/markdown"
                putExtra(Intent.EXTRA_TITLE, filename)
            }
            
            // For now, save to Downloads directory as a fallback
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            val file = File(downloadsDir, filename)
            
            FileWriter(file).use { writer ->
                writer.write(content)
            }
            
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }

    private fun saveHtmlFile(filename: String, content: String): Boolean {
        return try {
            // Save to Downloads directory
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            val file = File(downloadsDir, filename)
            
            FileWriter(file).use { writer ->
                writer.write(content)
            }
            
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }

    private fun savePdfFile(filename: String, htmlContent: String, result: MethodChannel.Result) {
        try {
            Log.d("MainActivity", "Starting PDF generation for file: $filename")
            
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            val file = File(downloadsDir, filename)
            
            // Create PDF document
            val document = PdfDocument()
            val pageInfo = PdfDocument.PageInfo.Builder(595, 842, 1).create() // A4 size
            val page = document.startPage(pageInfo)
            val canvas = page.canvas
            
            // Convert HTML to plain text (simple approach)
            val plainText = htmlContent
                .replace("<h1>", "\n\n")
                .replace("</h1>", "\n")
                .replace("<h2>", "\n\n")
                .replace("</h2>", "\n")
                .replace("<p>", "")
                .replace("</p>", "\n")
                .replace("<br>", "\n")
                .replace("<li>", "• ")
                .replace("</li>", "\n")
                .replace("<ul>", "\n")
                .replace("</ul>", "\n")
                .replace("<div[^>]*>", "")
                .replace("</div>", "\n")
                .replace("<hr>", "\n---\n")
                .replace("<[^>]+>".toRegex(), "") // Remove any remaining HTML tags
                .replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&quot;", "\"")
                .replace("&#x27;", "'")
                .trim()
            
            // Set up text paint
            val textPaint = TextPaint().apply {
                textSize = 12f
                color = android.graphics.Color.BLACK
                isAntiAlias = true
            }
            
            // Draw text on canvas
            val textWidth = pageInfo.pageWidth - 100 // Leave 50px margin on each side
            val staticLayout = StaticLayout.Builder.obtain(plainText, 0, plainText.length, textPaint, textWidth)
                .setAlignment(Layout.Alignment.ALIGN_NORMAL)
                .setLineSpacing(1.0f, 1.2f)
                .setIncludePad(false)
                .build()
            
            canvas.save()
            canvas.translate(50f, 50f) // Set margins
            staticLayout.draw(canvas)
            canvas.restore()
            
            // Finish the page
            document.finishPage(page)
            
            // Write PDF to file
            FileOutputStream(file).use { outputStream ->
                document.writeTo(outputStream)
            }
            
            // Close the document
            document.close()
            
            Log.d("MainActivity", "PDF generation completed successfully")
            result.success(true)
            
        } catch (e: Exception) {
            Log.e("MainActivity", "PDF generation failed", e)
            e.printStackTrace()
            result.success(false)
        }
    }

    private fun markdownToHtml(markdown: String): String {
        // Convert markdown to HTML
        var html = markdown
            .replace("# ", "<h1>")
            .replace("## ", "<h2>")
            .replace("### ", "<h3>")
            .replace("- ", "<li>")
            .replace("**", "<b>")
            .replace("*", "<i>")
            .replace("\n\n", "</p><p>")
            .replace("\n", "<br>")
        
        // Wrap in basic HTML structure
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Noeji Export</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h1 { color: #333; border-bottom: 2px solid #333; }
                    h2 { color: #666; border-bottom: 1px solid #666; }
                    li { margin-bottom: 5px; }
                </style>
            </head>
            <body>
                <p>$html</p>
            </body>
            </html>
        """.trimIndent()
    }
}
