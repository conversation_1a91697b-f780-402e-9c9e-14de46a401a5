# Export Ideabooks Feature Implementation

## Overview
This document describes the implementation of export features that allow users to export their content in various formats:
- **Export Ideabooks**: Export all ideabooks with their ideas and notes
- **Export Ideas**: Export only the ideas from a specific ideabook

## Feature Locations

### Export Ideabooks
- **Menu Location**: Context menu in the ideabook list page
- **Position**: Under "Settings" option and above other options
- **Access**: Available only for signed-in users

### Export Ideas
- **Menu Location**: Context menu in the idea list page (ideabook detail screen)
- **Position**: Last section in the menu, after other options
- **Access**: Available only for signed-in users

## Export Options

### 1. Export as Markdown
- **Icon**: `Icons.article` (text file icon)
- **File Format**: `.md` file with raw markdown syntax
- **Content Structure**:
  - Each ideabook becomes an H1 header (`# Ideabook Name`)
  - Ideas section becomes H2 header (`## Ideas`)
  - Notes section becomes H2 header (`## Notes`)
  - Each idea displayed as a list item (`- Idea content`)
  - Each note displayed as a list item (`- **Note Title**: Note content`)

### 2. Export as HTML
- **Icon**: `Icons.language` (HTML/web icon)
- **File Format**: `.html` file with proper HTML structure
- **Content Structure**:
  - Complete HTML document with CSS styling
  - Each ideabook becomes an H1 header (`<h1>Ideabook Name</h1>`)
  - Ideas section becomes H2 header (`<h2>Ideas</h2>`)
  - Notes section becomes H2 header (`<h2>Notes</h2>`)
  - Ideas and notes displayed as HTML lists (`<ul><li>content</li></ul>`)
  - Professional styling with typography and spacing

### 3. Export as PDF
- **Icon**: `Icons.picture_as_pdf` (PDF file icon)
- **File Format**: `.html` file optimized for PDF printing
- **Content Structure**: Same as HTML export but with print-optimized CSS styling
- **Note**: Exports as print-ready HTML that can be opened in browser and printed to PDF
- **Usage**: Open the exported HTML file in any browser and use "Print to PDF" function

## Implementation Details

### Flutter/Dart Layer

#### 1. UI Changes
- **File**: `lib/ui/widgets/context_menu.dart` - Export Ideabooks functionality
- **File**: `lib/ui/widgets/ideabook_detail_menu.dart` - Export Ideas functionality
- **Changes**: Added export sections with three export options and loading indicators
- **Additional Files**: `lib/ui/providers/export_state_provider.dart` - Manages export loading states

#### 2. Export Service
- **File**: `lib/services/export_service.dart`
- **Purpose**: Handles data retrieval and content generation
- **Key Methods**:
  - `exportAsMarkdown()`: Generates markdown content for all ideabooks with syntax fixing
  - `exportAsHtml()`: Generates complete HTML document for all ideabooks with styling
  - `exportAsPdfContent()`: Generates HTML content optimized for PDF (all ideabooks)
  - `exportIdeasAsMarkdown()`: Generates markdown content for ideas only from specific ideabook
  - `exportIdeasAsHtml()`: Generates HTML document for ideas only from specific ideabook
  - `exportIdeasAsPdfContent()`: Generates HTML content for ideas only (PDF format)
  - `_fixMarkdownSyntax()`: Fixes mismatched asterisks and markdown syntax errors
  - `_sanitizeForMarkdown()`: Sanitizes text for markdown export
  - `_sanitizeForHtml()`: Sanitizes text for HTML export (fixes markdown + escapes HTML)
  - `_escapeHtml()`: Helper method to escape HTML special characters

#### 3. File Export Utility
- **File**: `lib/utils/file_export_util.dart`
- **Purpose**: Handles platform-specific file operations
- **Key Methods**:
  - `exportMarkdownFile()`: Exports markdown files with proper .md extension
  - `exportHtmlFile()`: Exports HTML files with proper .html extension
  - `exportPdfFile()`: Exports PDF files with proper .pdf extension (saves as HTML content)
- **Features**: Configurable filename prefixes (ideabooks vs ideas), proper file extensions

### Platform-Specific Implementation

#### Android (Kotlin)
- **File**: `android/app/src/main/kotlin/com/blackstickyrice/noeji/MainActivity.kt`
- **Changes**:
  - Added `FILE_EXPORT_CHANNEL` method channel
  - Implemented `saveMarkdownFile()` method
  - Implemented `saveHtmlFile()` method
  - Implemented `savePdfFile()` method (saves as HTML for compatibility)
  - Added `markdownToHtml()` helper method

#### iOS (Swift)
- **File**: `ios/Runner/AppDelegate.swift`
- **Changes**:
  - Added file export method channel handler
  - Implemented `saveMarkdownFile()` method
  - Implemented `saveHtmlFile()` method
  - Implemented `savePdfFile()` method (saves as HTML for compatibility)
  - Added `markdownToHtml()` helper method
  - Added `UIDocumentPickerDelegate` extension

## Data Flow

1. **User Action**: User taps "Export as Markdown", "Export as HTML", or "Export as PDF"
2. **Data Retrieval**: Export service fetches all ideabooks, ideas, and notes
3. **Content Generation**: Service generates content in the appropriate format (markdown, HTML, or HTML for PDF)
4. **Platform Bridge**: Flutter calls native platform code via method channels
5. **File Creation**: Platform code creates temporary file with content
6. **Native Save Dialog**: Platform shows native file picker for user to choose save location
7. **User Feedback**: Success/failure message displayed via SnackBar

## Content Sorting and Structure

### Export Ideabooks
- **Ideabooks**: Displayed in same order as UI
- **Ideas**: Sorted by `sortOrder` if available, otherwise by `createdAt`
- **Notes**: Sorted by `sortOrder` if available, otherwise by `createdAt`

### Export Ideas
- **Ideas**: Sorted by `sortOrder` if available, otherwise by `createdAt`
- **Format**: Each idea content displayed directly, separated by horizontal dividers
- **Isolation**: Each idea is contained in its own section to prevent markdown formatting issues from spreading
- **No Numbering**: Ideas are exported without "Idea 1", "Idea 2" etc. titles, just the content

## Error Handling
- Network/database errors are caught and logged
- User-friendly error messages displayed via SnackBar
- Platform-specific errors handled in native code
- Markdown syntax errors automatically fixed to prevent formatting issues
- Export operations show loading indicators to provide user feedback

## Permissions
- **Android**: Uses existing `WRITE_EXTERNAL_STORAGE` permission
- **iOS**: Uses native UIDocumentPickerViewController (no additional permissions needed)

## Testing
- Run `flutter analyze` to check for code issues
- Test on both Android and iOS devices
- Verify file export functionality works correctly
- Test with different numbers of ideabooks, ideas, and notes

## File Naming Convention
- **Ideabooks Export**: `ideabooks_export_[timestamp].[extension]`
- **Ideas Export**: `ideas_export_[timestamp].[extension]`
- **PDF Export**: `[prefix]_export_for_pdf_[timestamp].html` (print-ready HTML files)
- **Extensions**: `.md` for Markdown, `.html` for HTML, `.html` for PDF-ready files

## Future Enhancements
- Add export format options (JSON, CSV, etc.)
- Add filtering options (export specific ideabooks, date ranges)
- Add custom styling options for PDF exports
- Add progress indicators for large exports
- Implement native PDF generation libraries (e.g., jsPDF, PDFKit)
- Add direct "Save as PDF" functionality without browser dependency
- Add batch export functionality