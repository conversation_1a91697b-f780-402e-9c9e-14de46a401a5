import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Markdown Syntax Fixing Tests', () {
    test('should escape mismatched asterisks', () {
      // Simulate the logic from ExportService._fixMarkdownSyntax
      String fixMarkdownSyntax(String text) {
        final asteriskCount = text.split('*').length - 1;
        String fixedText = text;
        
        // If odd number of asterisks, escape the last one
        if (asteriskCount % 2 != 0) {
          final lastAsteriskIndex = fixedText.lastIndexOf('*');
          if (lastAsteriskIndex != -1) {
            fixedText = '${fixedText.substring(0, lastAsteriskIndex)}\\*${fixedText.substring(lastAsteriskIndex + 1)}';
          }
        }
        
        return fixedText;
      }

      // Test cases
      expect(fixMarkdownSyntax('Hello * world'), 'Hello \\* world');
      expect(fixMarkdownSyntax('*bold* text'), '*bold* text'); // Even number, no change
      expect(fixMarkdownSyntax('*bold* text *'), '*bold* text \\*'); // Odd number, escape last
      expect(fixMarkdownSyntax('No asterisks'), 'No asterisks'); // No asterisks
      expect(fixMarkdownSyntax('*'), '\\*'); // Single asterisk
    });

    test('should handle edge cases', () {
      String fixMarkdownSyntax(String text) {
        final asteriskCount = text.split('*').length - 1;
        String fixedText = text;
        
        if (asteriskCount % 2 != 0) {
          final lastAsteriskIndex = fixedText.lastIndexOf('*');
          if (lastAsteriskIndex != -1) {
            fixedText = '${fixedText.substring(0, lastAsteriskIndex)}\\*${fixedText.substring(lastAsteriskIndex + 1)}';
          }
        }
        
        return fixedText;
      }

      expect(fixMarkdownSyntax(''), ''); // Empty string
      expect(fixMarkdownSyntax('**bold**'), '**bold**'); // Even number
      expect(fixMarkdownSyntax('***bold***'), '***bold***'); // Actually has 6 asterisks (even), so no change needed
      expect(fixMarkdownSyntax('**bold*'), '**bold\\*'); // 3 asterisks (odd)
    });
  });
}