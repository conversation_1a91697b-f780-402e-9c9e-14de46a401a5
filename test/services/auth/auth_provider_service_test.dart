import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:noeji/services/auth/auth_provider_service.dart';

// Generate mocks
@GenerateMocks([GoogleSignIn, GoogleSignInAccount, GoogleSignInAuthentication])
import 'auth_provider_service_test.mocks.dart';

void main() {
  group('AuthProviderService', () {
    late AuthProviderService authProviderService;
    late MockGoogleSignIn mockGoogleSignIn;

    setUp(() {
      mockGoogleSignIn = MockGoogleSignIn();
      authProviderService = AuthProviderService(googleSignIn: mockGoogleSignIn);
    });

    group('getGoogleCredential', () {
      test('returns null when user cancels Google sign-in', () async {
        // Arrange
        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => null);

        // Act
        final result = await authProviderService.getGoogleCredential();

        // Assert
        expect(result, isNull);
        verify(mockGoogleSignIn.signIn()).called(1);
      });

      test('returns AuthProviderResult when Google sign-in succeeds', () async {
        // Arrange
        final mockGoogleUser = MockGoogleSignInAccount();
        final mockGoogleAuth = MockGoogleSignInAuthentication();

        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockGoogleUser);
        when(mockGoogleUser.email).thenReturn('<EMAIL>');
        when(
          mockGoogleUser.authentication,
        ).thenAnswer((_) async => mockGoogleAuth);
        when(mockGoogleAuth.accessToken).thenReturn('mock_access_token');
        when(mockGoogleAuth.idToken).thenReturn('mock_id_token');

        // Act
        final result = await authProviderService.getGoogleCredential();

        // Assert
        expect(result, isNotNull);
        expect(result!.credential, isA<firebase_auth.AuthCredential>());
        expect(result.appleCredential, isNull);
        verify(mockGoogleSignIn.signIn()).called(1);
        verify(mockGoogleUser.authentication).called(1);
      });

      test('throws exception when access token is null', () async {
        // Arrange
        final mockGoogleUser = MockGoogleSignInAccount();
        final mockGoogleAuth = MockGoogleSignInAuthentication();

        when(mockGoogleSignIn.signIn()).thenAnswer((_) async => mockGoogleUser);
        when(mockGoogleUser.email).thenReturn('<EMAIL>');
        when(
          mockGoogleUser.authentication,
        ).thenAnswer((_) async => mockGoogleAuth);
        when(mockGoogleAuth.accessToken).thenReturn(null);

        // Act & Assert
        expect(
          () => authProviderService.getGoogleCredential(),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('No access token received from Google'),
            ),
          ),
        );
      });
    });

    group('getProviderFriendlyName', () {
      test('returns correct friendly names for supported providers', () {
        expect(
          AuthProviderService.getProviderFriendlyName('google.com'),
          equals('Google'),
        );
        expect(
          AuthProviderService.getProviderFriendlyName('apple.com'),
          equals('Apple'),
        );
        expect(
          AuthProviderService.getProviderFriendlyName('unknown.com'),
          equals('your sign-in provider'),
        );
      });
    });

    group('getCredentialForProvider', () {
      test('throws exception for unsupported provider', () async {
        // Act & Assert
        expect(
          () => authProviderService.getCredentialForProvider('facebook.com'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Unsupported provider: facebook.com'),
            ),
          ),
        );
      });
    });

    group('signOutGoogle', () {
      test('calls Google sign out without throwing', () async {
        // Arrange
        when(mockGoogleSignIn.signOut()).thenAnswer((_) async => null);

        // Act
        await authProviderService.signOutGoogle();

        // Assert
        verify(mockGoogleSignIn.signOut()).called(1);
      });

      test('handles Google sign out errors gracefully', () async {
        // Arrange
        when(
          mockGoogleSignIn.signOut(),
        ).thenThrow(Exception('Sign out failed'));

        // Act & Assert - should not throw
        await authProviderService.signOutGoogle();

        verify(mockGoogleSignIn.signOut()).called(1);
        return null; // Return null to satisfy the test function signature
      });
    });
  });
}
