# https://www.revenuecat.com/docs/api-v1#tag/entitlements/operation/grant-a-promotional-entitlement
# https://www.revenuecat.com/docs/api-v2#tag/Overview-(v2)

# Enum: "daily" "three_day" "weekly" "two_week" "monthly" "two_month" "three_month" "six_month" "yearly" "lifetime"

source ~/Development/revenuecat/tools.sh

curl -X POST "https://api.revenuecat.com/v1/subscribers/$CUSTOMER_ID/entitlements/$ENTITLEMENT_ID/promotional" \
     -H "Authorization: Bearer $RC_API_V1_KEY" \
     -H "Content-Type: application/json" \
     -d '{
           "duration": "lifetime"
         }' \

curl -X GET "https://api.revenuecat.com/v2/projects/$PROJECT_ID/customers/$CUSTOMER_ID/active_entitlements" \
     -H "Authorization: Bearer $RC_API_V2_KEY"
