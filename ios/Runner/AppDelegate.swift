import Flutter
import UIKit
import UniformTypeIdentifiers
import WebKit
import PDFKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    
    // Set up file export method channel
    let controller = window?.rootViewController as! FlutterViewController
    let fileExportChannel = FlutterMethodChannel(
      name: "com.noeji.app/file_export",
      binaryMessenger: controller.binaryMessenger
    )
    
    fileExportChannel.setMethodCallHandler { [weak self] call, result in
      switch call.method {
      case "saveMarkdownFile":
        if let args = call.arguments as? [String: Any],
           let filename = args["filename"] as? String,
           let content = args["content"] as? String {
          self?.saveMarkdownFile(filename: filename, content: content, result: result)
        } else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
        }
      case "saveHtmlFile":
        if let args = call.arguments as? [String: Any],
           let filename = args["filename"] as? String,
           let content = args["content"] as? String {
          self?.saveHtmlFile(filename: filename, content: content, result: result)
        } else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
        }
      case "savePdfFile":
        if let args = call.arguments as? [String: Any],
           let filename = args["filename"] as? String {
          // Support both parameter names for backward compatibility
          let htmlContent = args["htmlContent"] as? String ?? args["markdownContent"] as? String ?? ""
          self?.savePdfFile(filename: filename, htmlContent: htmlContent, result: result)
        } else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
        }
      default:
        result(FlutterMethodNotImplemented)
      }
    }
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  private func saveMarkdownFile(filename: String, content: String, result: @escaping FlutterResult) {
    let tempDirectory = FileManager.default.temporaryDirectory
    let tempFile = tempDirectory.appendingPathComponent(filename)
    
    do {
      try content.write(to: tempFile, atomically: true, encoding: .utf8)
      
      let documentPicker = UIDocumentPickerViewController(forExporting: [tempFile])
      documentPicker.delegate = self
      documentPicker.modalPresentationStyle = .formSheet
      
      if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
         let window = windowScene.windows.first {
        window.rootViewController?.present(documentPicker, animated: true)
        result(true)
      } else {
        result(false)
      }
    } catch {
      result(false)
    }
  }
  
  private func saveHtmlFile(filename: String, content: String, result: @escaping FlutterResult) {
    let tempDirectory = FileManager.default.temporaryDirectory
    let tempFile = tempDirectory.appendingPathComponent(filename)
    
    do {
      try content.write(to: tempFile, atomically: true, encoding: .utf8)
      
      let documentPicker = UIDocumentPickerViewController(forExporting: [tempFile])
      documentPicker.delegate = self
      documentPicker.modalPresentationStyle = .formSheet
      
      if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
         let window = windowScene.windows.first {
        window.rootViewController?.present(documentPicker, animated: true)
        result(true)
      } else {
        result(false)
      }
    } catch {
      result(false)
    }
  }
  
  private func savePdfFile(filename: String, htmlContent: String, result: @escaping FlutterResult) {
    // Create WKWebView to render HTML
    let webView = WKWebView()
    
    // Create a navigation delegate to handle page load completion
    let navigationDelegate = WebViewNavigationDelegate { [weak webView] in
      guard let webView = webView else {
        result(false)
        return
      }
      
      // Generate PDF after page is loaded
      DispatchQueue.main.async {
        let printFormatter = webView.viewPrintFormatter()
        
        // Set up print page renderer
        let render = UIPrintPageRenderer()
        render.addPrintFormatter(printFormatter, startingAtPageAt: 0)
        
        // Set up page size (A4)
        let page = CGRect(x: 0, y: 0, width: 595.2, height: 841.8) // A4 size in points
        render.setValue(page, forKey: "paperRect")
        render.setValue(page, forKey: "printableRect")
        
        // Create PDF data
        let pdfData = NSMutableData()
        UIGraphicsBeginPDFContextToData(pdfData, page, nil)
        
        for i in 0..<render.numberOfPages {
          UIGraphicsBeginPDFPage()
          render.drawPage(at: i, in: UIGraphicsGetPDFContextBounds())
        }
        
        UIGraphicsEndPDFContext()
        
        // Save PDF to temporary directory
        let tempDirectory = FileManager.default.temporaryDirectory
        let tempFile = tempDirectory.appendingPathComponent(filename)
        
        do {
          try pdfData.write(to: tempFile)
          
          let documentPicker = UIDocumentPickerViewController(forExporting: [tempFile])
          documentPicker.delegate = self
          documentPicker.modalPresentationStyle = .formSheet
          
          if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
             let window = windowScene.windows.first {
            window.rootViewController?.present(documentPicker, animated: true)
            result(true)
          } else {
            result(false)
          }
        } catch {
          result(false)
        }
      }
    }
    
    webView.navigationDelegate = navigationDelegate
    
    // Load HTML content
    webView.loadHTMLString(htmlContent, baseURL: nil)
  }
  
  private func markdownToHtml(_ markdown: String) -> String {
    // Convert markdown to HTML
    var html = markdown
      .replacingOccurrences(of: "# ", with: "<h1>")
      .replacingOccurrences(of: "## ", with: "<h2>")
      .replacingOccurrences(of: "### ", with: "<h3>")
      .replacingOccurrences(of: "- ", with: "<li>")
      .replacingOccurrences(of: "**", with: "<b>")
      .replacingOccurrences(of: "*", with: "<i>")
      .replacingOccurrences(of: "\n\n", with: "</p><p>")
      .replacingOccurrences(of: "\n", with: "<br>")
    
    // Wrap in basic HTML structure
    return """
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Noeji Export</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }
          h1 { color: #333; border-bottom: 2px solid #333; }
          h2 { color: #666; border-bottom: 1px solid #666; }
          li { margin-bottom: 5px; }
        </style>
      </head>
      <body>
        <p>\(html)</p>
      </body>
      </html>
    """
  }
}

// WebView navigation delegate for PDF generation
class WebViewNavigationDelegate: NSObject, WKNavigationDelegate {
  private let completion: () -> Void
  
  init(completion: @escaping () -> Void) {
    self.completion = completion
  }
  
  func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
    // Wait a bit for rendering to complete
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
      self.completion()
    }
  }
  
  func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
    completion()
  }
}

extension AppDelegate: UIDocumentPickerDelegate {
  func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
    // File export completed
  }
  
  func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
    // File export cancelled
  }
}
